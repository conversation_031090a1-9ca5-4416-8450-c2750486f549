import fs from 'fs-extra';
import path from 'path';
import { nanoid } from 'nanoid';
import { Session, SessionContext, AgentMessage, SessionStats } from '@/types';
import { config } from '@/config';
import { logger } from '@/utils/logger';
import { projectDiscovery } from '@/context/discovery';

export class SessionManager {
  private static instance: SessionManager;
  private currentSession: Session | null = null;
  private sessionsDir: string;

  private constructor() {
    this.sessionsDir = config.getSessionsDirectory();
    config.ensureDirectories();
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  public async createSession(
    name?: string, 
    workingDirectory?: string
  ): Promise<Session> {
    const sessionId = nanoid();
    const sessionName = name ?? `Session ${new Date().toLocaleString()}`;
    const workDir = workingDirectory ?? process.cwd();

    logger.info(`Creating new session: ${sessionName}`, { sessionId, workDir }, 'SessionManager');

    // Discover project context
    const projectContext = await projectDiscovery.discoverProject(workDir);
    const fileIndex = projectDiscovery.createFileIndex(projectContext.structure);

    const sessionContext: SessionContext = {
      projectStructure: projectContext.structure,
      fileIndex,
      dependencies: projectContext.dependencies,
      environment: process.env as Record<string, string>,
      ...(projectContext.gitInfo && { gitInfo: projectContext.gitInfo }),
    };

    const session: Session = {
      id: sessionId,
      name: sessionName,
      createdAt: new Date(),
      updatedAt: new Date(),
      workingDirectory: workDir,
      context: sessionContext,
      messages: [],
      metadata: {
        projectType: projectContext.type,
        totalFiles: projectContext.structure.totalFiles,
        totalDirectories: projectContext.structure.totalDirectories,
      },
    };

    await this.saveSession(session);
    this.currentSession = session;

    logger.info(`Session created successfully`, { 
      sessionId, 
      projectType: projectContext.type,
      files: projectContext.structure.totalFiles 
    }, 'SessionManager', sessionId);

    return session;
  }

  public async loadSession(sessionId: string): Promise<Session> {
    const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);

    if (!fs.existsSync(sessionPath)) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    try {
      const sessionData = await fs.readJson(sessionPath) as {
        id: string;
        name: string;
        createdAt: string;
        updatedAt: string;
        workingDirectory: string;
        context: {
          projectStructure: {
            directories: unknown[];
            files: unknown[];
            totalFiles: number;
            totalDirectories: number;
            lastIndexed: string;
          };
          fileIndex: {
            files: Array<[string, unknown]>;
            directories: Array<[string, unknown]>;
            byExtension: Array<[string, unknown]>;
            bySize: Array<[string, unknown]>;
            searchIndex: Array<[string, unknown]>;
          };
          dependencies: unknown[];
          environment: Record<string, string>;
          gitInfo?: unknown;
        };
        messages: unknown[];
        metadata: Record<string, unknown>;
      };
      const session = this.deserializeSession(sessionData);

      // Refresh project context if needed
      if (this.shouldRefreshContext(session)) {
        await this.refreshSessionContext(session);
      }

      // Restore working directory context
      this.restoreSessionContext(session);

      this.currentSession = session;

      logger.info(`Session loaded successfully`, {
        sessionId,
        messageCount: session.messages.length,
        workingDirectory: session.workingDirectory,
        projectType: session.metadata['projectType']
      }, 'SessionManager', sessionId);

      return session;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to load session: ${sessionId}`, error, 'SessionManager');
      throw new Error(`Failed to load session: ${errorMessage}`);
    }
  }

  private restoreSessionContext(session: Session): void {
    try {
      // Verify working directory still exists
      if (!fs.existsSync(session.workingDirectory)) {
        logger.warn(`Session working directory no longer exists: ${session.workingDirectory}`, undefined, 'SessionManager', session.id);
        // Fallback to current directory
        session.workingDirectory = process.cwd();
      }

      // Set up file watching for the session directory
      if (config.getConfig().context.watchFiles) {
        projectDiscovery.setupFileWatcher(session.workingDirectory);
      }

      // Update environment variables
      session.context.environment = {
        ...session.context.environment,
        ...process.env as Record<string, string>,
      };

      logger.debug(`Session context restored`, {
        sessionId: session.id,
        workingDirectory: session.workingDirectory
      }, 'SessionManager', session.id);
    } catch (error) {
      logger.error(`Failed to restore session context`, error, 'SessionManager', session.id);
    }
  }

  public async saveSession(session: Session): Promise<void> {
    try {
      session.updatedAt = new Date();
      const sessionPath = path.join(this.sessionsDir, `${session.id}.json`);
      const serializedSession = this.serializeSession(session);
      
      await fs.writeJson(sessionPath, serializedSession, { spaces: 2 });
      
      logger.debug(`Session saved`, { sessionId: session.id }, 'SessionManager', session.id);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to save session: ${session.id}`, error, 'SessionManager', session.id);
      throw new Error(`Failed to save session: ${errorMessage}`);
    }
  }

  public async deleteSession(sessionId: string): Promise<void> {
    const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
    
    if (fs.existsSync(sessionPath)) {
      await fs.remove(sessionPath);
      
      if (this.currentSession?.id === sessionId) {
        this.currentSession = null;
      }
      
      logger.info(`Session deleted`, { sessionId }, 'SessionManager');
    }
  }

  public async listSessions(): Promise<Session[]> {
    try {
      const files = await fs.readdir(this.sessionsDir);
      const sessionFiles = files.filter(file => file.endsWith('.json'));
      
      const sessions: Session[] = [];
      
      for (const file of sessionFiles) {
        try {
          const sessionPath = path.join(this.sessionsDir, file);
          const sessionData = await fs.readJson(sessionPath) as {
            id: string;
            name: string;
            createdAt: string;
            updatedAt: string;
            workingDirectory: string;
            context: {
              projectStructure: {
                directories: unknown[];
                files: unknown[];
                totalFiles: number;
                totalDirectories: number;
                lastIndexed: string;
              };
              fileIndex: {
                files: Array<[string, unknown]>;
                directories: Array<[string, unknown]>;
                byExtension: Array<[string, unknown]>;
                bySize: Array<[string, unknown]>;
                searchIndex: Array<[string, unknown]>;
              };
              dependencies: unknown[];
              environment: Record<string, string>;
              gitInfo?: unknown;
            };
            messages: unknown[];
            metadata: Record<string, unknown>;
          };
          const session = this.deserializeSession(sessionData);
          sessions.push(session);
        } catch (error) {
          logger.warn(`Failed to load session file: ${file}`, error, 'SessionManager');
        }
      }
      
      // Sort by updated date (most recent first)
      sessions.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
      
      return sessions;
    } catch (error) {
      logger.error('Failed to list sessions', error, 'SessionManager');
      return [];
    }
  }

  public getCurrentSession(): Session | null {
    return this.currentSession;
  }

  public async addMessage(message: AgentMessage): Promise<void> {
    if (!this.currentSession) {
      throw new Error('No active session');
    }

    this.currentSession.messages.push(message);
    
    // Limit message history
    const maxHistory = config.getConfig().session.maxHistory;
    if (this.currentSession.messages.length > maxHistory) {
      this.currentSession.messages = this.currentSession.messages.slice(-maxHistory);
    }

    if (config.getConfig().session.autoSave) {
      await this.saveSession(this.currentSession);
    }

    logger.debug(`Message added to session`, { 
      role: message.role,
      sessionId: this.currentSession.id 
    }, 'SessionManager', this.currentSession.id);
  }

  public getMessages(): AgentMessage[] {
    return this.currentSession?.messages ?? [];
  }

  public async refreshSessionContext(session: Session): Promise<void> {
    try {
      logger.info(`Refreshing session context`, { sessionId: session.id }, 'SessionManager', session.id);
      
      const projectContext = await projectDiscovery.refreshProject(session.workingDirectory);
      const fileIndex = projectDiscovery.createFileIndex(projectContext.structure);

      session.context = {
        projectStructure: projectContext.structure,
        fileIndex,
        dependencies: projectContext.dependencies,
        environment: process.env as Record<string, string>,
        ...(projectContext.gitInfo && { gitInfo: projectContext.gitInfo }),
      };

      session.metadata = {
        ...session.metadata,
        projectType: projectContext.type,
        totalFiles: projectContext.structure.totalFiles,
        totalDirectories: projectContext.structure.totalDirectories,
      };

      await this.saveSession(session);
      
      logger.info(`Session context refreshed`, { 
        sessionId: session.id,
        files: projectContext.structure.totalFiles 
      }, 'SessionManager', session.id);
    } catch (error) {
      logger.error(`Failed to refresh session context`, error, 'SessionManager', session.id);
      throw error;
    }
  }

  private shouldRefreshContext(session: Session): boolean {
    const maxAge = 10 * 60 * 1000; // 10 minutes
    const age = Date.now() - session.context.projectStructure.lastIndexed.getTime();
    return age > maxAge;
  }

  private serializeSession(session: Session): {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    workingDirectory: string;
    context: {
      projectStructure: {
        directories: unknown[];
        files: unknown[];
        totalFiles: number;
        totalDirectories: number;
        lastIndexed: string;
      };
      fileIndex: {
        files: Array<[string, unknown]>;
        directories: Array<[string, unknown]>;
        byExtension: Array<[string, unknown]>;
        bySize: Array<[string, unknown]>;
        searchIndex: Array<[string, unknown]>;
      };
      dependencies: unknown[];
      environment: Record<string, string>;
      gitInfo?: unknown;
    };
    messages: unknown[];
    metadata: Record<string, unknown>;
  } {
    return {
      ...session,
      createdAt: session.createdAt.toISOString(),
      updatedAt: session.updatedAt.toISOString(),
      context: {
        ...session.context,
        projectStructure: {
          ...session.context.projectStructure,
          lastIndexed: session.context.projectStructure.lastIndexed.toISOString(),
        },
        fileIndex: {
          files: Array.from(session.context.fileIndex.files.entries()),
          directories: Array.from(session.context.fileIndex.directories.entries()),
          byExtension: Array.from(session.context.fileIndex.byExtension.entries()),
          bySize: Array.from(session.context.fileIndex.bySize.entries()),
          searchIndex: Array.from(session.context.fileIndex.searchIndex.entries()),
        },
      },
    };
  }

  private deserializeSession(data: {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    workingDirectory: string;
    context: {
      projectStructure: {
        directories: unknown[];
        files: unknown[];
        totalFiles: number;
        totalDirectories: number;
        lastIndexed: string;
      };
      fileIndex: {
        files: Array<[string, unknown]>;
        directories: Array<[string, unknown]>;
        byExtension: Array<[string, unknown]>;
        bySize: Array<[string, unknown]>;
        searchIndex: Array<[string, unknown]>;
      };
      dependencies: unknown[];
      environment: Record<string, string>;
      gitInfo?: unknown;
    };
    messages: unknown[];
    metadata: Record<string, unknown>;
  }): Session {
    return {
      ...data,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
      context: {
        ...data.context,
        projectStructure: {
          ...data.context.projectStructure,
          lastIndexed: new Date(data.context.projectStructure.lastIndexed),
        },
        fileIndex: {
          files: new Map(data.context.fileIndex.files),
          directories: new Map(data.context.fileIndex.directories),
          byExtension: new Map(data.context.fileIndex.byExtension),
          bySize: new Map(data.context.fileIndex.bySize),
          searchIndex: new Map(data.context.fileIndex.searchIndex),
        },
      },
    } as Session;
  }

  public async exportSession(sessionId: string, outputPath: string): Promise<void> {
    const session = await this.loadSession(sessionId);
    const exportData = {
      session: this.serializeSession(session),
      exportedAt: new Date().toISOString(),
      version: '1.0.0',
    };

    await fs.writeJson(outputPath, exportData, { spaces: 2 });
    logger.info(`Session exported`, { sessionId, outputPath }, 'SessionManager', sessionId);
  }

  public async importSession(importPath: string): Promise<Session> {
    const importData = await fs.readJson(importPath) as {
      session: {
        id: string;
        name: string;
        createdAt: string;
        updatedAt: string;
        workingDirectory: string;
        context: {
          projectStructure: {
            directories: unknown[];
            files: unknown[];
            totalFiles: number;
            totalDirectories: number;
            lastIndexed: string;
          };
          fileIndex: {
            files: Array<[string, unknown]>;
            directories: Array<[string, unknown]>;
            byExtension: Array<[string, unknown]>;
            bySize: Array<[string, unknown]>;
            searchIndex: Array<[string, unknown]>;
          };
          dependencies: unknown[];
          environment: Record<string, string>;
          gitInfo?: unknown;
        };
        messages: unknown[];
        metadata: Record<string, unknown>;
      };
      exportedAt: string;
      version: string;
    };
    const session = this.deserializeSession(importData.session);
    
    // Generate new ID to avoid conflicts
    session.id = nanoid();
    session.name = `${session.name} (Imported)`;
    
    await this.saveSession(session);
    logger.info(`Session imported`, { sessionId: session.id }, 'SessionManager', session.id);
    
    return session;
  }

  public async cleanupOldSessions(maxAge: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    const sessions = await this.listSessions();
    const cutoffDate = new Date(Date.now() - maxAge);

    let deletedCount = 0;
    for (const session of sessions) {
      if (session.updatedAt < cutoffDate) {
        await this.deleteSession(session.id);
        deletedCount++;
      }
    }

    logger.info(`Cleaned up ${deletedCount} old sessions`, undefined, 'SessionManager');
  }

  public async getSessionStats(sessionId?: string): Promise<SessionStats> {
    try {
      const session = sessionId ? await this.loadSession(sessionId) : this.currentSession;
      if (!session) {
        throw new Error('No session found');
      }

      const messageStats = {
        total: session.messages.length,
        byRole: session.messages.reduce((acc, msg) => {
          acc[msg.role] = (acc[msg.role] ?? 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        withToolCalls: session.messages.filter(msg => msg.toolCalls && msg.toolCalls.length > 0).length,
      };

      const contextStats = {
        files: session.context.projectStructure.totalFiles,
        directories: session.context.projectStructure.totalDirectories,
        dependencies: session.context.dependencies.length,
        lastIndexed: session.context.projectStructure.lastIndexed,
      };

      const sessionAge = Date.now() - session.createdAt.getTime();
      const lastActivity = Date.now() - session.updatedAt.getTime();

      return {
        session: {
          id: session.id,
          name: session.name,
          age: sessionAge,
          lastActivity,
          workingDirectory: session.workingDirectory,
        },
        messages: messageStats,
        context: contextStats,
        metadata: session.metadata,
      };
    } catch (error) {
      logger.error('Failed to get session stats', error, 'SessionManager');
      throw error;
    }
  }

  public async optimizeSession(sessionId?: string): Promise<void> {
    try {
      const session = sessionId ? await this.loadSession(sessionId) : this.currentSession;
      if (!session) {
        throw new Error('No session found');
      }

      logger.info(`Optimizing session`, { sessionId: session.id }, 'SessionManager', session.id);

      const maxHistory = config.getConfig().session.maxHistory;
      const originalMessageCount = session.messages.length;

      // Keep system messages and recent messages
      const systemMessages = session.messages.filter(msg => msg.role === 'system');
      const recentMessages = session.messages.slice(-Math.floor(maxHistory * 0.8));

      // Combine and deduplicate
      const optimizedMessages = [
        ...systemMessages,
        ...recentMessages.filter(msg => msg.role !== 'system')
      ];

      session.messages = optimizedMessages;
      session.updatedAt = new Date();

      // Update metadata
      session.metadata['optimized'] = true;
      session.metadata['lastOptimization'] = new Date().toISOString();
      session.metadata['messagesRemoved'] = originalMessageCount - optimizedMessages.length;

      await this.saveSession(session);

      logger.info(`Session optimized`, {
        sessionId: session.id,
        originalMessages: originalMessageCount,
        optimizedMessages: optimizedMessages.length,
        removed: originalMessageCount - optimizedMessages.length
      }, 'SessionManager', session.id);
    } catch (error) {
      logger.error('Failed to optimize session', error, 'SessionManager');
      throw error;
    }
  }

  public async backupSession(sessionId?: string): Promise<string> {
    try {
      const session = sessionId ? await this.loadSession(sessionId) : this.currentSession;
      if (!session) {
        throw new Error('No session found');
      }

      const backupDir = path.join(config.getSessionsDirectory(), 'backups');
      await fs.ensureDir(backupDir);

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = path.join(backupDir, `${session.id}_${timestamp}.json`);

      await this.exportSession(session.id, backupPath);

      logger.info(`Session backed up`, { sessionId: session.id, backupPath }, 'SessionManager', session.id);
      return backupPath;
    } catch (error) {
      logger.error('Failed to backup session', error, 'SessionManager');
      throw error;
    }
  }
}

export const sessionManager = SessionManager.getInstance();

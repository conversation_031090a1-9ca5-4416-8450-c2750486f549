import fs from 'fs-extra';
import path from 'path';
import { glob } from 'glob';
import chokidar from 'chokidar';
import mime from 'mime-types';
import { EventEmitter } from 'events';
import {
  ProjectContext,
  ProjectType,
  ProjectStructure,
  FileNode,
  DirectoryNode,
  DependencyInfo,
  ProjectConfiguration,
  GitInfo,
  FileIndex,
  FilePermissions
} from '@/types';
import { logger } from '@/utils/logger';
import { config } from '@/config';

export class ProjectDiscovery extends EventEmitter {
  private watchers: Map<string, chokidar.FSWatcher> = new Map();
  private indexCache: Map<string, ProjectContext> = new Map();

  constructor() {
    super();
  }

  public async discoverProject(rootPath: string): Promise<ProjectContext> {
    logger.info(`Starting project discovery for: ${rootPath}`, undefined, 'ProjectDiscovery');

    const absolutePath = path.resolve(rootPath);
    
    if (!fs.existsSync(absolutePath)) {
      throw new Error(`Path does not exist: ${absolutePath}`);
    }

    const stat = await fs.stat(absolutePath);
    if (!stat.isDirectory()) {
      throw new Error(`Path is not a directory: ${absolutePath}`);
    }

    // Check cache first
    const cached = this.indexCache.get(absolutePath);
    if (cached && this.isCacheValid(cached)) {
      logger.debug('Using cached project context', undefined, 'ProjectDiscovery');
      return cached;
    }

    const projectType = await this.detectProjectType(absolutePath);
    const structure = await this.buildProjectStructure(absolutePath);
    const dependencies = await this.extractDependencies(absolutePath, projectType);
    const configuration = await this.extractConfiguration(absolutePath, projectType);
    const gitInfo = await this.extractGitInfo(absolutePath);

    const context: ProjectContext = {
      root: absolutePath,
      type: projectType,
      structure,
      dependencies,
      configuration,
      gitInfo,
    };

    // Cache the result
    this.indexCache.set(absolutePath, context);

    // Set up file watching if enabled
    if (config.getConfig().context.watchFiles) {
      this.setupFileWatcher(absolutePath);
    }

    logger.info(`Project discovery completed for ${projectType} project`, {
      files: structure.totalFiles,
      directories: structure.totalDirectories,
      dependencies: dependencies.length
    }, 'ProjectDiscovery');

    return context;
  }

  private async detectProjectType(rootPath: string): Promise<ProjectType> {
    const files = await fs.readdir(rootPath);
    
    // Check for specific project files
    if (files.includes('package.json')) {
      const packageJson = await this.safeReadJson(path.join(rootPath, 'package.json'));
      if (packageJson) {
        const deps = packageJson['dependencies'] as Record<string, string> | undefined;
        const devDeps = packageJson['devDependencies'] as Record<string, string> | undefined;
        if (deps?.['react'] ?? devDeps?.['react']) {
          return 'web';
        }
      }
      return 'nodejs';
    }
    
    if (files.includes('requirements.txt') || files.includes('pyproject.toml') || files.includes('setup.py')) {
      return 'python';
    }
    
    if (files.includes('Cargo.toml')) {
      return 'rust';
    }
    
    if (files.includes('go.mod')) {
      return 'go';
    }
    
    if (files.includes('pom.xml') || files.includes('build.gradle')) {
      return 'java';
    }
    
    if (files.includes('*.csproj') || files.includes('*.sln')) {
      return 'csharp';
    }
    
    if (files.includes('CMakeLists.txt') || files.includes('Makefile')) {
      return 'cpp';
    }

    // Check for mobile projects
    if (files.includes('android') || files.includes('ios')) {
      return 'mobile';
    }

    return 'unknown';
  }

  private async buildProjectStructure(rootPath: string): Promise<ProjectStructure> {
    const excludePatterns = config.getConfig().context.excludePatterns;
    const maxFileSize = config.getConfig().context.maxFileSize;

    const allFiles = await glob('**/*', {
      cwd: rootPath,
      ignore: excludePatterns,
      dot: false,
      absolute: false,
    });

    const files: FileNode[] = [];
    const directories: DirectoryNode[] = [];
    const dirMap = new Map<string, DirectoryNode>();

    for (const relativePath of allFiles) {
      const fullPath = path.join(rootPath, relativePath);
      const stat = await fs.stat(fullPath);

      if (stat.isDirectory()) {
        const dirNode: DirectoryNode = {
          path: relativePath,
          name: path.basename(relativePath),
          children: [],
          permissions: await this.getFilePermissions(fullPath),
        };
        directories.push(dirNode);
        dirMap.set(relativePath, dirNode);
      } else if (stat.isFile() && stat.size <= maxFileSize) {
        const fileNode: FileNode = {
          path: relativePath,
          name: path.basename(relativePath),
          size: stat.size,
          extension: path.extname(relativePath),
          mimeType: mime.lookup(relativePath) || 'application/octet-stream',
          lastModified: stat.mtime,
          permissions: await this.getFilePermissions(fullPath),
        };

        // Add content for text files under a certain size
        if (this.isTextFile(fileNode) && stat.size < 10000) {
          try {
            fileNode.content = await fs.readFile(fullPath, 'utf-8');
          } catch (error) {
            logger.debug(`Could not read file content: ${fullPath}`, error, 'ProjectDiscovery');
          }
        }

        files.push(fileNode);
      }
    }

    // Build directory tree
    this.buildDirectoryTree(directories, files, dirMap);

    return {
      directories: directories.filter(dir => !dir.path.includes('/')), // Root level directories
      files: files.filter(file => !file.path.includes('/')), // Root level files
      totalFiles: files.length,
      totalDirectories: directories.length,
      lastIndexed: new Date(),
    };
  }

  private buildDirectoryTree(
    directories: DirectoryNode[], 
    files: FileNode[], 
    dirMap: Map<string, DirectoryNode>
  ): void {
    // Add files to their parent directories
    for (const file of files) {
      const parentPath = path.dirname(file.path);
      if (parentPath !== '.' && dirMap.has(parentPath)) {
        const parentDir = dirMap.get(parentPath);
        if (parentDir) {
          parentDir.children.push(file);
        }
      }
    }

    // Add subdirectories to their parent directories
    for (const dir of directories) {
      const parentPath = path.dirname(dir.path);
      if (parentPath !== '.' && dirMap.has(parentPath)) {
        const parentDir = dirMap.get(parentPath);
        if (parentDir) {
          parentDir.children.push(dir);
        }
      }
    }
  }

  private async getFilePermissions(filePath: string): Promise<FilePermissions> {
    try {
      const stat = await fs.stat(filePath);
      const mode = stat.mode.toString(8);
      
      return {
        readable: !!(stat.mode & parseInt('400', 8)),
        writable: !!(stat.mode & parseInt('200', 8)),
        executable: !!(stat.mode & parseInt('100', 8)),
        mode,
      };
    } catch (error) {
      return {
        readable: false,
        writable: false,
        executable: false,
        mode: '000',
      };
    }
  }

  private isTextFile(file: FileNode): boolean {
    const textExtensions = [
      '.txt', '.md', '.json', '.js', '.ts', '.jsx', '.tsx', '.py', '.rs', '.go',
      '.java', '.cs', '.cpp', '.c', '.h', '.hpp', '.css', '.scss', '.sass',
      '.html', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
      '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd'
    ];

    return textExtensions.includes(file.extension.toLowerCase()) ||
           file.mimeType.startsWith('text/') ||
           file.mimeType.includes('json') ||
           file.mimeType.includes('xml');
  }

  private async extractDependencies(rootPath: string, projectType: ProjectType): Promise<DependencyInfo[]> {
    const dependencies: DependencyInfo[] = [];

    try {
      switch (projectType) {
        case 'nodejs':
        case 'web': {
          const packageJson = await this.safeReadJson(path.join(rootPath, 'package.json'));
          if (packageJson) {
            this.extractNpmDependencies(packageJson, dependencies);
          }
          break;
        }

        case 'python':
          await this.extractPythonDependencies(rootPath, dependencies);
          break;

        case 'rust':
          await this.extractRustDependencies(rootPath, dependencies);
          break;

        case 'go':
          await this.extractGoDependencies(rootPath, dependencies);
          break;

        case 'java':
          await this.extractJavaDependencies(rootPath, dependencies);
          break;
      }
    } catch (error) {
      logger.warn('Failed to extract dependencies', error, 'ProjectDiscovery');
    }

    return dependencies;
  }

  private extractNpmDependencies(packageJson: Record<string, unknown>, dependencies: DependencyInfo[]): void {
    const deps = (packageJson['dependencies'] as Record<string, string>) || {};
    const devDeps = (packageJson['devDependencies'] as Record<string, string>) || {};
    const peerDeps = (packageJson['peerDependencies'] as Record<string, string>) || {};
    const optionalDeps = (packageJson['optionalDependencies'] as Record<string, string>) || {};

    for (const [name, version] of Object.entries(deps)) {
      dependencies.push({
        name,
        version,
        type: 'production',
        source: 'package.json',
      });
    }

    for (const [name, version] of Object.entries(devDeps)) {
      dependencies.push({
        name,
        version,
        type: 'development',
        source: 'package.json',
      });
    }

    for (const [name, version] of Object.entries(peerDeps)) {
      dependencies.push({
        name,
        version,
        type: 'peer',
        source: 'package.json',
      });
    }

    for (const [name, version] of Object.entries(optionalDeps)) {
      dependencies.push({
        name,
        version,
        type: 'optional',
        source: 'package.json',
      });
    }
  }

  private async extractPythonDependencies(rootPath: string, dependencies: DependencyInfo[]): Promise<void> {
    // Try requirements.txt
    const requirementsPath = path.join(rootPath, 'requirements.txt');
    if (fs.existsSync(requirementsPath)) {
      const content = await fs.readFile(requirementsPath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));

      for (const line of lines) {
        const match = line.match(/^([a-zA-Z0-9_-]+)([>=<~!]+)(.+)$/);
        if (match?.[1] && match[3]) {
          dependencies.push({
            name: match[1],
            version: match[3],
            type: 'production',
            source: 'requirements.txt',
          });
        }
      }
    }

    // Try pyproject.toml
    const pyprojectPath = path.join(rootPath, 'pyproject.toml');
    if (fs.existsSync(pyprojectPath)) {
      // Basic TOML parsing for dependencies
      const content = await fs.readFile(pyprojectPath, 'utf-8');
      const dependencySection = content.match(/\[tool\.poetry\.dependencies\]([\s\S]*?)(?=\[|$)/);
      if (dependencySection?.[1]) {
        const lines = dependencySection[1].split('\n').filter(line => line.trim());
        for (const line of lines) {
          const match = line.match(/^([a-zA-Z0-9_-]+)\s*=\s*"([^"]+)"/);
          if (match?.[1] && match[2]) {
            dependencies.push({
              name: match[1],
              version: match[2],
              type: 'production',
              source: 'pyproject.toml',
            });
          }
        }
      }
    }
  }

  private async extractRustDependencies(rootPath: string, dependencies: DependencyInfo[]): Promise<void> {
    const cargoPath = path.join(rootPath, 'Cargo.toml');
    if (fs.existsSync(cargoPath)) {
      const content = await fs.readFile(cargoPath, 'utf-8');

      // Basic TOML parsing for dependencies
      const dependencySection = content.match(/\[dependencies\]([\s\S]*?)(?=\[|$)/);
      if (dependencySection?.[1]) {
        const lines = dependencySection[1].split('\n').filter(line => line.trim());
        for (const line of lines) {
          const match = line.match(/^([a-zA-Z0-9_-]+)\s*=\s*"([^"]+)"/);
          if (match?.[1] && match[2]) {
            dependencies.push({
              name: match[1],
              version: match[2],
              type: 'production',
              source: 'Cargo.toml',
            });
          }
        }
      }
    }
  }

  private async extractGoDependencies(rootPath: string, dependencies: DependencyInfo[]): Promise<void> {
    const goModPath = path.join(rootPath, 'go.mod');
    if (fs.existsSync(goModPath)) {
      const content = await fs.readFile(goModPath, 'utf-8');
      const lines = content.split('\n');

      let inRequireBlock = false;
      for (const line of lines) {
        const trimmed = line.trim();

        if (trimmed === 'require (') {
          inRequireBlock = true;
          continue;
        }

        if (trimmed === ')' && inRequireBlock) {
          inRequireBlock = false;
          continue;
        }

        if (inRequireBlock || trimmed.startsWith('require ')) {
          const match = trimmed.match(/([^\s]+)\s+([^\s]+)/);
          if (match?.[1] && match[2]) {
            dependencies.push({
              name: match[1],
              version: match[2],
              type: 'production',
              source: 'go.mod',
            });
          }
        }
      }
    }
  }

  private async extractJavaDependencies(rootPath: string, dependencies: DependencyInfo[]): Promise<void> {
    // Try Maven pom.xml
    const pomPath = path.join(rootPath, 'pom.xml');
    if (fs.existsSync(pomPath)) {
      const content = await fs.readFile(pomPath, 'utf-8');
      // Basic XML parsing for Maven dependencies
      const dependencyMatches = content.matchAll(/<dependency>([\s\S]*?)<\/dependency>/g);

      for (const match of dependencyMatches) {
        const depContent = match[1];
        if (depContent) {
          const groupId = depContent.match(/<groupId>(.*?)<\/groupId>/)?.[1];
          const artifactId = depContent.match(/<artifactId>(.*?)<\/artifactId>/)?.[1];
          const version = depContent.match(/<version>(.*?)<\/version>/)?.[1];

          if (groupId && artifactId && version) {
            dependencies.push({
              name: `${groupId}:${artifactId}`,
              version,
              type: 'production',
              source: 'pom.xml',
            });
          }
        }
      }
    }
  }

  private async extractConfiguration(rootPath: string, projectType: ProjectType): Promise<ProjectConfiguration> {
    const config: ProjectConfiguration = {};

    try {
      switch (projectType) {
        case 'nodejs':
        case 'web':
          await this.extractNodeConfiguration(rootPath, config);
          break;
        case 'python':
          await this.extractPythonConfiguration(rootPath, config);
          break;
        case 'rust':
          await this.extractRustConfiguration(rootPath, config);
          break;
        case 'go':
          await this.extractGoConfiguration(rootPath, config);
          break;
      }
    } catch (error) {
      logger.warn('Failed to extract configuration', error, 'ProjectDiscovery');
    }

    return config;
  }

  private async extractNodeConfiguration(rootPath: string, config: ProjectConfiguration): Promise<void> {
    const packageJson = await this.safeReadJson(path.join(rootPath, 'package.json'));
    if (packageJson) {
      config.language = 'javascript';
      const version = packageJson['version'] as string | undefined;
      if (version) {
        config.version = version;
      }

      // Detect package manager
      if (fs.existsSync(path.join(rootPath, 'yarn.lock'))) {
        config.packageManager = 'yarn';
      } else if (fs.existsSync(path.join(rootPath, 'pnpm-lock.yaml'))) {
        config.packageManager = 'pnpm';
      } else {
        config.packageManager = 'npm';
      }

      // Detect framework
      const dependencies = (packageJson['dependencies'] as Record<string, string>) || {};
      const devDependencies = (packageJson['devDependencies'] as Record<string, string>) || {};
      const deps = { ...dependencies, ...devDependencies };
      if (deps['react']) config.framework = 'react';
      else if (deps['vue']) config.framework = 'vue';
      else if (deps['angular']) config.framework = 'angular';
      else if (deps['svelte']) config.framework = 'svelte';
      else if (deps['next']) config.framework = 'nextjs';
      else if (deps['nuxt']) config.framework = 'nuxt';

      // Detect build tools
      if (deps['webpack']) config.bundler = 'webpack';
      else if (deps['vite']) config.bundler = 'vite';
      else if (deps['rollup']) config.bundler = 'rollup';
      else if (deps['parcel']) config.bundler = 'parcel';

      // Detect test framework
      if (deps['jest']) config.testFramework = 'jest';
      else if (deps['mocha']) config.testFramework = 'mocha';
      else if (deps['vitest']) config.testFramework = 'vitest';

      // Detect linter
      if (deps['eslint']) config.linter = 'eslint';
      if (deps['prettier']) config.formatter = 'prettier';

      // Check for TypeScript
      if (deps['typescript'] ?? fs.existsSync(path.join(rootPath, 'tsconfig.json'))) {
        config.language = 'typescript';
      }
    }
  }

  private async extractPythonConfiguration(rootPath: string, config: ProjectConfiguration): Promise<void> {
    config.language = 'python';

    // Detect package manager
    if (fs.existsSync(path.join(rootPath, 'pyproject.toml'))) {
      const content = await fs.readFile(path.join(rootPath, 'pyproject.toml'), 'utf-8');
      if (content.includes('[tool.poetry]')) {
        config.packageManager = 'poetry';
      } else if (content.includes('[build-system]')) {
        config.packageManager = 'pip';
      }
    } else if (fs.existsSync(path.join(rootPath, 'requirements.txt'))) {
      config.packageManager = 'pip';
    }

    // Detect framework
    const requirementsPath = path.join(rootPath, 'requirements.txt');
    if (fs.existsSync(requirementsPath)) {
      const content = await fs.readFile(requirementsPath, 'utf-8');
      if (content.includes('django')) config.framework = 'django';
      else if (content.includes('flask')) config.framework = 'flask';
      else if (content.includes('fastapi')) config.framework = 'fastapi';
    }
  }

  private async extractRustConfiguration(rootPath: string, config: ProjectConfiguration): Promise<void> {
    config.language = 'rust';
    config.packageManager = 'cargo';
    config.buildTool = 'cargo';

    const cargoToml = await this.safeReadToml(path.join(rootPath, 'Cargo.toml'));
    if (cargoToml) {
      const packageInfo = cargoToml['package'] as Record<string, unknown> | undefined;
      const version = packageInfo?.['version'] as string | undefined;
      if (version) {
        config.version = version;
      }
    }
  }

  private async extractGoConfiguration(rootPath: string, config: ProjectConfiguration): Promise<void> {
    config.language = 'go';
    config.packageManager = 'go mod';
    config.buildTool = 'go build';

    const goModPath = path.join(rootPath, 'go.mod');
    if (fs.existsSync(goModPath)) {
      const content = await fs.readFile(goModPath, 'utf-8');
      const versionMatch = content.match(/go (\d+\.\d+)/);
      if (versionMatch?.[1]) {
        config.version = versionMatch[1];
      }
    }
  }

  private async extractGitInfo(rootPath: string): Promise<GitInfo | undefined> {
    try {
      const gitDir = path.join(rootPath, '.git');
      if (!fs.existsSync(gitDir)) {
        return undefined;
      }

      const headPath = path.join(gitDir, 'HEAD');
      if (!fs.existsSync(headPath)) {
        return undefined;
      }

      const headContent = await fs.readFile(headPath, 'utf-8');
      const branch = headContent.startsWith('ref: refs/heads/')
        ? headContent.replace('ref: refs/heads/', '').trim()
        : 'detached';

      // Get current commit
      let commit = '';
      if (branch !== 'detached') {
        const branchRefPath = path.join(gitDir, 'refs', 'heads', branch);
        if (fs.existsSync(branchRefPath)) {
          commit = (await fs.readFile(branchRefPath, 'utf-8')).trim();
        }
      } else {
        commit = headContent.trim();
      }

      return {
        branch,
        commit: commit.slice(0, 7), // Short commit hash
        isRepo: true,
        status: {
          staged: [],
          unstaged: [],
          untracked: [],
          ahead: 0,
          behind: 0,
        },
      };
    } catch (error) {
      logger.debug('Failed to extract git info', error, 'ProjectDiscovery');
      return undefined;
    }
  }

  private async safeReadJson(filePath: string): Promise<Record<string, unknown> | null> {
    try {
      if (fs.existsSync(filePath)) {
        const content = await fs.readFile(filePath, 'utf-8');
        return JSON.parse(content) as Record<string, unknown>;
      }
    } catch (error) {
      logger.debug(`Failed to read JSON file: ${filePath}`, error, 'ProjectDiscovery');
    }
    return null;
  }

  private async safeReadToml(filePath: string): Promise<Record<string, unknown> | null> {
    try {
      if (fs.existsSync(filePath)) {
        const content = await fs.readFile(filePath, 'utf-8');
        // Basic TOML parsing - you might want to use a proper TOML library
        const result: Record<string, unknown> = {};
        const lines = content.split('\n');
        let currentSection = result;

        for (const line of lines) {
          const trimmed = line.trim();
          if (!trimmed || trimmed.startsWith('#')) continue;

          if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
            const sectionName = trimmed.slice(1, -1);
            const parts = sectionName.split('.');
            currentSection = result;

            for (const part of parts) {
              if (!currentSection[part]) {
                currentSection[part] = {};
              }
              currentSection = currentSection[part] as Record<string, unknown>;
            }
          } else {
            const match = trimmed.match(/^([^=]+)=(.+)$/);
            if (match?.[1] && match[2]) {
              const key = match[1].trim();
              let value = match[2].trim();

              // Remove quotes
              if ((value.startsWith('"') && value.endsWith('"')) ||
                  (value.startsWith("'") && value.endsWith("'"))) {
                value = value.slice(1, -1);
              }

              currentSection[key] = value;
            }
          }
        }

        return result;
      }
    } catch (error) {
      logger.debug(`Failed to read TOML file: ${filePath}`, error, 'ProjectDiscovery');
    }
    return null;
  }

  private isCacheValid(context: ProjectContext): boolean {
    const maxAge = 5 * 60 * 1000; // 5 minutes
    const age = Date.now() - context.structure.lastIndexed.getTime();
    return age < maxAge;
  }

  public setupFileWatcher(rootPath: string): void {
    if (this.watchers.has(rootPath)) {
      return; // Already watching
    }

    const excludePatterns = config.getConfig().context.excludePatterns;

    const watcher = chokidar.watch(rootPath, {
      ignored: excludePatterns,
      persistent: true,
      ignoreInitial: true,
    });

    watcher.on('all', (event, filePath) => {
      logger.debug(`File ${event}: ${filePath}`, undefined, 'ProjectDiscovery');
      // Invalidate cache when files change
      this.indexCache.delete(rootPath);

      // Emit event for real-time updates
      this.emit('fileChange', { event, filePath, rootPath });
    });

    this.watchers.set(rootPath, watcher);
    logger.info(`Started watching files in: ${rootPath}`, undefined, 'ProjectDiscovery');
  }

  public createFileIndex(structure: ProjectStructure): FileIndex {
    const fileIndex: FileIndex = {
      files: new Map(),
      directories: new Map(),
      byExtension: new Map(),
      bySize: new Map(),
      searchIndex: new Map(),
    };

    const processNode = (node: FileNode | DirectoryNode, parentPath = '') => {
      const fullPath = parentPath ? `${parentPath}/${node.path}` : node.path;

      if ('size' in node) {
        // It's a file
        const file = node;
        fileIndex.files.set(fullPath, file);

        // Index by extension
        const ext = file.extension.toLowerCase();
        if (!fileIndex.byExtension.has(ext)) {
          fileIndex.byExtension.set(ext, []);
        }
        const extFiles = fileIndex.byExtension.get(ext);
        if (extFiles) {
          extFiles.push(file);
        }

        // Index by size category
        const sizeCategory = this.getSizeCategory(file.size);
        if (!fileIndex.bySize.has(sizeCategory)) {
          fileIndex.bySize.set(sizeCategory, []);
        }
        const sizeFiles = fileIndex.bySize.get(sizeCategory);
        if (sizeFiles) {
          sizeFiles.push(file);
        }

        // Create search index
        const searchTerms = [
          file.name.toLowerCase(),
          file.extension.toLowerCase(),
          ...file.name.toLowerCase().split(/[._-]/)
        ];

        for (const term of searchTerms) {
          if (!fileIndex.searchIndex.has(term)) {
            fileIndex.searchIndex.set(term, []);
          }
          const termFiles = fileIndex.searchIndex.get(term);
          if (termFiles) {
            termFiles.push(file);
          }
        }
      } else {
        // It's a directory
        const dir = node;
        fileIndex.directories.set(fullPath, dir);

        // Process children
        for (const child of dir.children) {
          processNode(child, fullPath);
        }
      }
    };

    // Process root level items
    for (const file of structure.files) {
      processNode(file);
    }

    for (const dir of structure.directories) {
      processNode(dir);
    }

    return fileIndex;
  }

  private getSizeCategory(size: number): string {
    if (size < 1024) return 'tiny'; // < 1KB
    if (size < 10 * 1024) return 'small'; // < 10KB
    if (size < 100 * 1024) return 'medium'; // < 100KB
    if (size < 1024 * 1024) return 'large'; // < 1MB
    return 'huge'; // >= 1MB
  }

  public async refreshProject(rootPath: string): Promise<ProjectContext> {
    this.indexCache.delete(rootPath);
    return this.discoverProject(rootPath);
  }

  public stopWatching(rootPath: string): void {
    const watcher = this.watchers.get(rootPath);
    if (watcher) {
      void watcher.close();
      this.watchers.delete(rootPath);
      logger.info(`Stopped watching files in: ${rootPath}`, undefined, 'ProjectDiscovery');
    }
  }

  public stopAllWatching(): void {
    for (const [rootPath, watcher] of this.watchers) {
      void watcher.close();
      logger.info(`Stopped watching files in: ${rootPath}`, undefined, 'ProjectDiscovery');
    }
    this.watchers.clear();
  }
}

export const projectDiscovery = new ProjectDiscovery();

import { EventEmitter } from 'events';
import { ExecutionContext } from '../types';
export interface ShellExecutionResult {
    success: boolean;
    message: string;
    data?: {
        stdout: string;
        stderr: string;
        exitCode: number;
        command: string;
        workingDirectory: string;
        duration: number;
        pid?: number | undefined;
    };
    error?: string;
}
export interface ShellExecutionOptions {
    timeout?: number;
    cwd?: string;
    env?: Record<string, string>;
    shell?: boolean;
    detached?: boolean;
    stdio?: 'pipe' | 'inherit' | 'ignore';
}
export declare class ShellOperations extends EventEmitter {
    private static instance;
    private runningProcesses;
    static getInstance(): ShellOperations;
    executeCommand(command: string, context: ExecutionContext, options?: ShellExecutionOptions): Promise<ShellExecutionResult>;
    executeScript(script: string, context: ExecutionContext, options?: ShellExecutionOptions & {
        interpreter?: string;
    }): Promise<ShellExecutionResult>;
    killProcess(pid: number): boolean;
    killAllProcesses(): number;
    getRunningProcesses(): Array<{
        pid: number;
        command?: string;
        startTime?: Date;
        status?: string;
    }>;
    getProcessInfo(pid: number): Promise<any>;
    executeCommandWithRealTimeOutput(command: string, context: ExecutionContext, options?: ShellExecutionOptions & {
        onStdout?: (data: string) => void;
        onStderr?: (data: string) => void;
    }): Promise<ShellExecutionResult>;
    private validateCommand;
    private parseCommand;
}
export declare const shellOperations: ShellOperations;
//# sourceMappingURL=shell-ops.d.ts.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepseekProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class DeepseekProvider {
    name = 'deepseek';
    client;
    constructor() {
        this.client = axios_1.default.create({
            timeout: 60000,
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
    validateConfig(config) {
        return !!(config.apiKey && config.baseUrl);
    }
    async sendMessage(messages, config) {
        try {
            const response = await this.client.post(`${config.baseUrl}/chat/completions`, {
                model: config.model,
                messages: this.formatMessages(messages),
                temperature: config.temperature || 0.7,
                max_tokens: config.maxTokens || 4000,
                stream: false,
            }, {
                headers: {
                    Authorization: `Bearer ${config.apiKey}`,
                },
            });
            const content = response.data.choices[0]?.message?.content;
            if (!content) {
                throw new Error('No content in response');
            }
            logger_1.logger.debug('Deepseek response received', {
                model: config.model,
                tokens: response.data.usage
            }, 'DeepseekProvider');
            return content;
        }
        catch (error) {
            logger_1.logger.error('Deepseek API error', error, 'DeepseekProvider');
            if (error.response?.status === 401) {
                throw new Error('Invalid API key for Deepseek');
            }
            if (error.response?.status === 429) {
                throw new Error('Rate limit exceeded for Deepseek API');
            }
            throw new Error(`Deepseek API error: ${error.message}`);
        }
    }
    async sendToolMessage(messages, tools, config) {
        try {
            const response = await this.client.post(`${config.baseUrl}/chat/completions`, {
                model: config.model,
                messages: this.formatMessages(messages),
                tools: this.formatTools(tools),
                tool_choice: 'auto',
                temperature: config.temperature || 0.7,
                max_tokens: config.maxTokens || 4000,
                stream: false,
            }, {
                headers: {
                    Authorization: `Bearer ${config.apiKey}`,
                },
            });
            const choice = response.data.choices[0];
            const message = choice?.message;
            if (!message) {
                throw new Error('No message in response');
            }
            logger_1.logger.debug('Deepseek tool response received', {
                model: config.model,
                hasToolCalls: !!message.tool_calls,
                toolCallsCount: message.tool_calls?.length || 0
            }, 'DeepseekProvider');
            return {
                message: message.content || '',
                toolCalls: message.tool_calls?.map((call) => ({
                    id: call.id,
                    type: 'function',
                    function: {
                        name: call.function.name,
                        arguments: call.function.arguments,
                    },
                })),
            };
        }
        catch (error) {
            logger_1.logger.error('Deepseek tool API error', error, 'DeepseekProvider');
            if (error.response?.status === 401) {
                throw new Error('Invalid API key for Deepseek');
            }
            if (error.response?.status === 429) {
                throw new Error('Rate limit exceeded for Deepseek API');
            }
            throw new Error(`Deepseek API error: ${error.message}`);
        }
    }
    formatMessages(messages) {
        return messages.map(msg => {
            if (msg.role === 'tool') {
                return {
                    role: 'tool',
                    content: msg.content,
                    tool_call_id: msg.toolCallId,
                };
            }
            const formatted = {
                role: msg.role,
                content: msg.content,
            };
            if (msg.toolCalls) {
                formatted.tool_calls = msg.toolCalls.map(call => ({
                    id: call.id,
                    type: call.type,
                    function: {
                        name: call.function.name,
                        arguments: call.function.arguments,
                    },
                }));
            }
            return formatted;
        });
    }
    formatTools(tools) {
        return tools.map(tool => ({
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: this.zodToJsonSchema(tool.parameters),
            },
        }));
    }
    zodToJsonSchema(schema) {
        // Basic Zod to JSON Schema conversion
        // This is a simplified version - you might want to use a proper library
        if (schema._def.typeName === 'ZodObject') {
            const properties = {};
            const required = [];
            for (const [key, value] of Object.entries(schema._def.shape())) {
                properties[key] = this.zodToJsonSchema(value);
                if (!value._def.optional) {
                    required.push(key);
                }
            }
            return {
                type: 'object',
                properties,
                required: required.length > 0 ? required : undefined,
            };
        }
        if (schema._def.typeName === 'ZodString') {
            return { type: 'string' };
        }
        if (schema._def.typeName === 'ZodNumber') {
            return { type: 'number' };
        }
        if (schema._def.typeName === 'ZodBoolean') {
            return { type: 'boolean' };
        }
        if (schema._def.typeName === 'ZodArray') {
            return {
                type: 'array',
                items: this.zodToJsonSchema(schema._def.type),
            };
        }
        if (schema._def.typeName === 'ZodOptional') {
            return this.zodToJsonSchema(schema._def.innerType);
        }
        return { type: 'string' }; // fallback
    }
}
exports.DeepseekProvider = DeepseekProvider;
//# sourceMappingURL=deepseek.js.map
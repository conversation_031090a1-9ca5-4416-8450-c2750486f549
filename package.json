{"name": "agentic-cli", "version": "1.0.0", "description": "Production-ready AI-powered CLI tool system with agentic capabilities", "main": "dist/cli.js", "bin": {"agentic": "dist/cli.js"}, "scripts": {"build": "tsc && tsc-alias", "dev": "tsx src/cli.ts", "start": "node dist/cli.js", "watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build"}, "keywords": ["ai", "cli", "agent", "automation", "typescript", "llm"], "author": "Agentic CLI Team", "license": "MIT", "dependencies": {"axios": "^1.6.7", "chalk": "^5.3.0", "chokidar": "^3.6.0", "commander": "^12.0.0", "cross-spawn": "^7.0.3", "date-fns": "^3.3.1", "dotenv": "^16.4.5", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "glob": "^10.3.10", "inquirer": "^9.2.15", "mime-types": "^2.1.35", "nanoid": "^5.0.6", "ora": "^8.0.1", "semver": "^7.6.0", "sqlite3": "^5.1.6", "strip-ansi": "^7.1.0", "terminal-kit": "^3.0.1", "yaml": "^2.4.1", "zod": "^3.22.4"}, "devDependencies": {"@types/cross-spawn": "^6.0.6", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/node": "^20.11.24", "@types/semver": "^7.5.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.2", "tsc-alias": "^1.8.16", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/agentic-cli.git"}, "bugs": {"url": "https://github.com/your-org/agentic-cli/issues"}, "homepage": "https://github.com/your-org/agentic-cli#readme"}
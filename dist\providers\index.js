"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaProvider = exports.DeepseekProvider = exports.providerManager = exports.ProviderManager = void 0;
const deepseek_1 = require("./deepseek");
Object.defineProperty(exports, "DeepseekProvider", { enumerable: true, get: function () { return deepseek_1.DeepseekProvider; } });
const ollama_1 = require("./ollama");
Object.defineProperty(exports, "OllamaProvider", { enumerable: true, get: function () { return ollama_1.OllamaProvider; } });
class ProviderManager {
    providers = new Map();
    constructor() {
        this.registerProvider(new deepseek_1.DeepseekProvider());
        this.registerProvider(new ollama_1.OllamaProvider());
    }
    registerProvider(provider) {
        this.providers.set(provider.name, provider);
    }
    getProvider(name) {
        return this.providers.get(name);
    }
    getAvailableProviders() {
        return Array.from(this.providers.keys());
    }
    hasProvider(name) {
        return this.providers.has(name);
    }
}
exports.ProviderManager = ProviderManager;
exports.providerManager = new ProviderManager();
//# sourceMappingURL=index.js.map
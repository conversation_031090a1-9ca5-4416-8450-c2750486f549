"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test setup file for Jest
const config_1 = require("./config");
const logger_1 = require("./utils/logger");
// Set up test environment
beforeAll(() => {
    // Set log level to error to reduce noise during tests
    logger_1.logger.setLogLevel(logger_1.LogLevel.ERROR);
    // Ensure test directories exist
    config_1.config.ensureDirectories();
});
// Clean up after tests
afterAll(() => {
    // Clean up any test data if needed
});
// Mock console methods to reduce noise during tests
global.console = {
    ...console,
    log: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
//# sourceMappingURL=test-setup.js.map
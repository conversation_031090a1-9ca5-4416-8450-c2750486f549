import { EventEmitter } from 'events';
import { ProjectContext, ProjectStructure, FileIndex } from '../types';
export declare class ProjectDiscovery extends EventEmitter {
    private watchers;
    private indexCache;
    constructor();
    discoverProject(rootPath: string): Promise<ProjectContext>;
    private detectProjectType;
    private buildProjectStructure;
    private buildDirectoryTree;
    private getFilePermissions;
    private isTextFile;
    private extractDependencies;
    private extractNpmDependencies;
    private extractPythonDependencies;
    private extractRustDependencies;
    private extractGoDependencies;
    private extractJavaDependencies;
    private extractConfiguration;
    private extractNodeConfiguration;
    private extractPythonConfiguration;
    private extractRustConfiguration;
    private extractGoConfiguration;
    private extractGitInfo;
    private safeReadJson;
    private safeReadToml;
    private isCacheValid;
    setupFileWatcher(rootPath: string): void;
    createFileIndex(structure: ProjectStructure): FileIndex;
    private getSizeCategory;
    refreshProject(rootPath: string): Promise<ProjectContext>;
    stopWatching(rootPath: string): void;
    stopAllWatching(): void;
}
export declare const projectDiscovery: ProjectDiscovery;
//# sourceMappingURL=discovery.d.ts.map
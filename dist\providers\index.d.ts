import { LLMProvider } from '../types';
import { DeepseekProvider } from './deepseek';
import { OllamaProvider } from './ollama';
export declare class ProviderManager {
    private providers;
    constructor();
    registerProvider(provider: LLMProvider): void;
    getProvider(name: string): LLMProvider | undefined;
    getAvailableProviders(): string[];
    hasProvider(name: string): boolean;
}
export declare const providerManager: ProviderManager;
export { DeepseekProvider, OllamaProvider };
//# sourceMappingURL=index.d.ts.map
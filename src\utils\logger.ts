import fs from 'fs-extra';
import path from 'path';
import { config } from '@/config';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  message: string;
  data?: unknown;
  sessionId?: string;
  component?: string;
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel = LogLevel.INFO;
  private logFile: string;

  private constructor() {
    const logsDir = config.getLogsDirectory();
    this.logFile = path.join(logsDir, `agentic-cli-${new Date().toISOString().split('T')[0]}.log`);
    fs.ensureDirSync(logsDir);
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  public debug(message: string, data?: unknown, component?: string, sessionId?: string): void {
    this.log(LogLevel.DEBUG, message, data, component, sessionId);
  }

  public info(message: string, data?: unknown, component?: string, sessionId?: string): void {
    this.log(LogLevel.INFO, message, data, component, sessionId);
  }

  public warn(message: string, data?: unknown, component?: string, sessionId?: string): void {
    this.log(LogLevel.WARN, message, data, component, sessionId);
  }

  public error(message: string, data?: unknown, component?: string, sessionId?: string): void {
    this.log(LogLevel.ERROR, message, data, component, sessionId);
  }

  private log(level: LogLevel, message: string, data?: unknown, component?: string, sessionId?: string): void {
    if (level < this.logLevel) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      ...(data !== undefined && { data }),
      ...(component !== undefined && { component }),
      ...(sessionId !== undefined && { sessionId }),
    };

    // Console output
    this.logToConsole(entry);

    // File output
    this.logToFile(entry);
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const levelStr = LogLevel[entry.level];
    const component = entry.component ? `[${entry.component}]` : '';
    const sessionId = entry.sessionId ? `(${entry.sessionId.slice(0, 8)})` : '';
    
    const prefix = `${timestamp} ${levelStr} ${component}${sessionId}`;
    const message = `${prefix} ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(message, entry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(message, entry.data || '');
        break;
      case LogLevel.ERROR:
        console.error(message, entry.data || '');
        break;
    }
  }

  private logToFile(entry: LogEntry): void {
    try {
      const logLine = `${JSON.stringify(entry)}\n`;
      fs.appendFileSync(this.logFile, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  public getLogs(sessionId?: string, component?: string, level?: LogLevel): LogEntry[] {
    try {
      const logContent = fs.readFileSync(this.logFile, 'utf-8');
      const lines = logContent.trim().split('\n').filter(line => line.trim());
      
      let logs = lines.map(line => {
        try {
          return JSON.parse(line) as LogEntry;
        } catch {
          return null;
        }
      }).filter(Boolean) as LogEntry[];

      if (sessionId) {
        logs = logs.filter(log => log.sessionId === sessionId);
      }

      if (component) {
        logs = logs.filter(log => log.component === component);
      }

      if (level !== undefined) {
        logs = logs.filter(log => log.level >= level);
      }

      return logs;
    } catch (error) {
      console.error('Failed to read log file:', error);
      return [];
    }
  }

  public clearLogs(): void {
    try {
      fs.writeFileSync(this.logFile, '');
    } catch (error) {
      console.error('Failed to clear log file:', error);
    }
  }

  public getLogFile(): string {
    return this.logFile;
  }
}

export const logger = Logger.getInstance();

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionError = exports.ToolError = exports.AgentError = void 0;
// Error Types
class AgentError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'AgentError';
    }
}
exports.AgentError = AgentError;
class ToolError extends Error {
    toolName;
    details;
    constructor(message, toolName, details) {
        super(message);
        this.toolName = toolName;
        this.details = details;
        this.name = 'ToolError';
    }
}
exports.ToolError = ToolError;
class SessionError extends Error {
    sessionId;
    details;
    constructor(message, sessionId, details) {
        super(message);
        this.sessionId = sessionId;
        this.details = details;
        this.name = 'SessionError';
    }
}
exports.SessionError = SessionError;
//# sourceMappingURL=index.js.map
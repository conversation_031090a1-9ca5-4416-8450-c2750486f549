"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = exports.LogLevel = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const config_1 = require("../config");
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    static instance;
    logLevel = LogLevel.INFO;
    logFile;
    constructor() {
        const logsDir = config_1.config.getLogsDirectory();
        this.logFile = path_1.default.join(logsDir, `agentic-cli-${new Date().toISOString().split('T')[0]}.log`);
        fs_extra_1.default.ensureDirSync(logsDir);
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    setLogLevel(level) {
        this.logLevel = level;
    }
    debug(message, data, component, sessionId) {
        this.log(LogLevel.DEBUG, message, data, component, sessionId);
    }
    info(message, data, component, sessionId) {
        this.log(LogLevel.INFO, message, data, component, sessionId);
    }
    warn(message, data, component, sessionId) {
        this.log(LogLevel.WARN, message, data, component, sessionId);
    }
    error(message, data, component, sessionId) {
        this.log(LogLevel.ERROR, message, data, component, sessionId);
    }
    log(level, message, data, component, sessionId) {
        if (level < this.logLevel) {
            return;
        }
        const entry = {
            timestamp: new Date(),
            level,
            message,
            ...(data !== undefined && { data }),
            ...(component !== undefined && { component }),
            ...(sessionId !== undefined && { sessionId }),
        };
        // Console output
        this.logToConsole(entry);
        // File output
        this.logToFile(entry);
    }
    logToConsole(entry) {
        const timestamp = entry.timestamp.toISOString();
        const levelStr = LogLevel[entry.level];
        const component = entry.component ? `[${entry.component}]` : '';
        const sessionId = entry.sessionId ? `(${entry.sessionId.slice(0, 8)})` : '';
        const prefix = `${timestamp} ${levelStr} ${component}${sessionId}`;
        const message = `${prefix} ${entry.message}`;
        switch (entry.level) {
            case LogLevel.DEBUG:
                console.debug(message, entry.data || '');
                break;
            case LogLevel.INFO:
                console.info(message, entry.data || '');
                break;
            case LogLevel.WARN:
                console.warn(message, entry.data || '');
                break;
            case LogLevel.ERROR:
                console.error(message, entry.data || '');
                break;
        }
    }
    logToFile(entry) {
        try {
            const logLine = JSON.stringify(entry) + '\n';
            fs_extra_1.default.appendFileSync(this.logFile, logLine);
        }
        catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }
    getLogs(sessionId, component, level) {
        try {
            const logContent = fs_extra_1.default.readFileSync(this.logFile, 'utf-8');
            const lines = logContent.trim().split('\n').filter(line => line.trim());
            let logs = lines.map(line => {
                try {
                    return JSON.parse(line);
                }
                catch {
                    return null;
                }
            }).filter(Boolean);
            if (sessionId) {
                logs = logs.filter(log => log.sessionId === sessionId);
            }
            if (component) {
                logs = logs.filter(log => log.component === component);
            }
            if (level !== undefined) {
                logs = logs.filter(log => log.level >= level);
            }
            return logs;
        }
        catch (error) {
            console.error('Failed to read log file:', error);
            return [];
        }
    }
    clearLogs() {
        try {
            fs_extra_1.default.writeFileSync(this.logFile, '');
        }
        catch (error) {
            console.error('Failed to clear log file:', error);
        }
    }
    getLogFile() {
        return this.logFile;
    }
}
exports.Logger = Logger;
exports.logger = Logger.getInstance();
//# sourceMappingURL=logger.js.map
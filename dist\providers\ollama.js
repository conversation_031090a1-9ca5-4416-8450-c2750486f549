"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../utils/logger");
class OllamaProvider {
    name = 'ollama';
    client;
    constructor() {
        this.client = axios_1.default.create({
            timeout: 120000, // Ollama can be slower
            headers: {
                'Content-Type': 'application/json',
            },
        });
    }
    validateConfig(config) {
        return !!(config.baseUrl && config.model);
    }
    async sendMessage(messages, config) {
        try {
            // First check if model is available
            await this.ensureModelAvailable(config);
            const response = await this.client.post(`${config.baseUrl}/api/chat`, {
                model: config.model,
                messages: this.formatMessages(messages),
                stream: false,
                options: {
                    temperature: config.temperature || 0.7,
                    num_predict: config.maxTokens || 4000,
                },
            });
            const content = response.data.message?.content;
            if (!content) {
                throw new Error('No content in response');
            }
            logger_1.logger.debug('Ollama response received', {
                model: config.model,
                done: response.data.done
            }, 'OllamaProvider');
            return content;
        }
        catch (error) {
            logger_1.logger.error('Ollama API error', error, 'OllamaProvider');
            if (error.code === 'ECONNREFUSED') {
                throw new Error('Cannot connect to Ollama. Make sure Ollama is running.');
            }
            if (error.response?.status === 404) {
                throw new Error(`Model ${config.model} not found in Ollama`);
            }
            throw new Error(`Ollama API error: ${error.message}`);
        }
    }
    async sendToolMessage(messages, tools, config) {
        try {
            await this.ensureModelAvailable(config);
            // Ollama doesn't have native tool calling, so we'll use a prompt-based approach
            const toolsPrompt = this.createToolsPrompt(tools);
            const enhancedMessages = [
                {
                    role: 'system',
                    content: toolsPrompt,
                },
                ...messages,
            ];
            const response = await this.client.post(`${config.baseUrl}/api/chat`, {
                model: config.model,
                messages: this.formatMessages(enhancedMessages),
                stream: false,
                options: {
                    temperature: config.temperature || 0.7,
                    num_predict: config.maxTokens || 4000,
                },
            });
            const content = response.data.message?.content;
            if (!content) {
                throw new Error('No content in response');
            }
            // Parse tool calls from the response
            const { message, toolCalls } = this.parseToolCalls(content);
            logger_1.logger.debug('Ollama tool response received', {
                model: config.model,
                hasToolCalls: toolCalls.length > 0,
                toolCallsCount: toolCalls.length
            }, 'OllamaProvider');
            const result = {
                message,
            };
            if (toolCalls.length > 0) {
                result.toolCalls = toolCalls;
            }
            return result;
        }
        catch (error) {
            logger_1.logger.error('Ollama tool API error', error, 'OllamaProvider');
            if (error.code === 'ECONNREFUSED') {
                throw new Error('Cannot connect to Ollama. Make sure Ollama is running.');
            }
            if (error.response?.status === 404) {
                throw new Error(`Model ${config.model} not found in Ollama`);
            }
            throw new Error(`Ollama API error: ${error.message}`);
        }
    }
    async ensureModelAvailable(config) {
        try {
            const response = await this.client.get(`${config.baseUrl}/api/tags`);
            const models = response.data.models || [];
            const modelExists = models.some((model) => model.name === config.model);
            if (!modelExists) {
                logger_1.logger.info(`Model ${config.model} not found, attempting to pull...`, undefined, 'OllamaProvider');
                await this.pullModel(config);
            }
        }
        catch (error) {
            logger_1.logger.warn('Could not check model availability', error, 'OllamaProvider');
        }
    }
    async pullModel(config) {
        try {
            await this.client.post(`${config.baseUrl}/api/pull`, {
                name: config.model,
            });
            logger_1.logger.info(`Successfully pulled model ${config.model}`, undefined, 'OllamaProvider');
        }
        catch (error) {
            throw new Error(`Failed to pull model ${config.model}: ${error}`);
        }
    }
    formatMessages(messages) {
        return messages.map(msg => ({
            role: msg.role === 'tool' ? 'assistant' : msg.role,
            content: msg.content,
        }));
    }
    createToolsPrompt(tools) {
        const toolDescriptions = tools.map(tool => {
            return `Tool: ${tool.name}
Description: ${tool.description}
Parameters: ${JSON.stringify(this.zodToJsonSchema(tool.parameters), null, 2)}`;
        }).join('\n\n');
        return `You are an AI assistant with access to tools. When you need to use a tool, respond with a JSON object in this exact format:

{
  "tool_calls": [
    {
      "id": "unique_id",
      "type": "function",
      "function": {
        "name": "tool_name",
        "arguments": "{\"param1\": \"value1\"}"
      }
    }
  ]
}

Available tools:
${toolDescriptions}

If you don't need to use any tools, respond normally without the JSON format.`;
    }
    parseToolCalls(content) {
        try {
            // Try to find JSON in the response
            const jsonMatch = content.match(/\{[\s\S]*"tool_calls"[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                if (parsed.tool_calls && Array.isArray(parsed.tool_calls)) {
                    return {
                        message: content.replace(jsonMatch[0], '').trim(),
                        toolCalls: parsed.tool_calls.map((call, index) => ({
                            id: call.id || `call_${Date.now()}_${index}`,
                            type: 'function',
                            function: {
                                name: call.function.name,
                                arguments: call.function.arguments,
                            },
                        })),
                    };
                }
            }
        }
        catch (error) {
            logger_1.logger.debug('Failed to parse tool calls from response', error, 'OllamaProvider');
        }
        return {
            message: content,
            toolCalls: [],
        };
    }
    zodToJsonSchema(schema) {
        // Basic Zod to JSON Schema conversion
        if (schema._def.typeName === 'ZodObject') {
            const properties = {};
            const required = [];
            for (const [key, value] of Object.entries(schema._def.shape())) {
                properties[key] = this.zodToJsonSchema(value);
                if (!value._def.optional) {
                    required.push(key);
                }
            }
            return {
                type: 'object',
                properties,
                required: required.length > 0 ? required : undefined,
            };
        }
        if (schema._def.typeName === 'ZodString') {
            return { type: 'string' };
        }
        if (schema._def.typeName === 'ZodNumber') {
            return { type: 'number' };
        }
        if (schema._def.typeName === 'ZodBoolean') {
            return { type: 'boolean' };
        }
        if (schema._def.typeName === 'ZodArray') {
            return {
                type: 'array',
                items: this.zodToJsonSchema(schema._def.type),
            };
        }
        if (schema._def.typeName === 'ZodOptional') {
            return this.zodToJsonSchema(schema._def.innerType);
        }
        return { type: 'string' }; // fallback
    }
}
exports.OllamaProvider = OllamaProvider;
//# sourceMappingURL=ollama.js.map
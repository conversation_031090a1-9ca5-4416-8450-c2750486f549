"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.projectDiscovery = exports.ProjectDiscovery = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const glob_1 = require("glob");
const chokidar_1 = __importDefault(require("chokidar"));
const mime_types_1 = __importDefault(require("mime-types"));
const events_1 = require("events");
const logger_1 = require("../utils/logger");
const config_1 = require("../config");
class ProjectDiscovery extends events_1.EventEmitter {
    watchers = new Map();
    indexCache = new Map();
    constructor() {
        super();
    }
    async discoverProject(rootPath) {
        logger_1.logger.info(`Starting project discovery for: ${rootPath}`, undefined, 'ProjectDiscovery');
        const absolutePath = path_1.default.resolve(rootPath);
        if (!fs_extra_1.default.existsSync(absolutePath)) {
            throw new Error(`Path does not exist: ${absolutePath}`);
        }
        const stat = await fs_extra_1.default.stat(absolutePath);
        if (!stat.isDirectory()) {
            throw new Error(`Path is not a directory: ${absolutePath}`);
        }
        // Check cache first
        const cached = this.indexCache.get(absolutePath);
        if (cached && this.isCacheValid(cached)) {
            logger_1.logger.debug('Using cached project context', undefined, 'ProjectDiscovery');
            return cached;
        }
        const projectType = await this.detectProjectType(absolutePath);
        const structure = await this.buildProjectStructure(absolutePath);
        const dependencies = await this.extractDependencies(absolutePath, projectType);
        const configuration = await this.extractConfiguration(absolutePath, projectType);
        const gitInfo = await this.extractGitInfo(absolutePath);
        const context = {
            root: absolutePath,
            type: projectType,
            structure,
            dependencies,
            configuration,
            gitInfo,
        };
        // Cache the result
        this.indexCache.set(absolutePath, context);
        // Set up file watching if enabled
        if (config_1.config.getConfig().context.watchFiles) {
            this.setupFileWatcher(absolutePath);
        }
        logger_1.logger.info(`Project discovery completed for ${projectType} project`, {
            files: structure.totalFiles,
            directories: structure.totalDirectories,
            dependencies: dependencies.length
        }, 'ProjectDiscovery');
        return context;
    }
    async detectProjectType(rootPath) {
        const files = await fs_extra_1.default.readdir(rootPath);
        // Check for specific project files
        if (files.includes('package.json')) {
            const packageJson = await this.safeReadJson(path_1.default.join(rootPath, 'package.json'));
            if (packageJson?.dependencies?.react || packageJson?.devDependencies?.react) {
                return 'web';
            }
            return 'nodejs';
        }
        if (files.includes('requirements.txt') || files.includes('pyproject.toml') || files.includes('setup.py')) {
            return 'python';
        }
        if (files.includes('Cargo.toml')) {
            return 'rust';
        }
        if (files.includes('go.mod')) {
            return 'go';
        }
        if (files.includes('pom.xml') || files.includes('build.gradle')) {
            return 'java';
        }
        if (files.includes('*.csproj') || files.includes('*.sln')) {
            return 'csharp';
        }
        if (files.includes('CMakeLists.txt') || files.includes('Makefile')) {
            return 'cpp';
        }
        // Check for mobile projects
        if (files.includes('android') || files.includes('ios')) {
            return 'mobile';
        }
        return 'unknown';
    }
    async buildProjectStructure(rootPath) {
        const excludePatterns = config_1.config.getConfig().context.excludePatterns;
        const maxFileSize = config_1.config.getConfig().context.maxFileSize;
        const allFiles = await (0, glob_1.glob)('**/*', {
            cwd: rootPath,
            ignore: excludePatterns,
            dot: false,
            absolute: false,
        });
        const files = [];
        const directories = [];
        const dirMap = new Map();
        for (const relativePath of allFiles) {
            const fullPath = path_1.default.join(rootPath, relativePath);
            const stat = await fs_extra_1.default.stat(fullPath);
            if (stat.isDirectory()) {
                const dirNode = {
                    path: relativePath,
                    name: path_1.default.basename(relativePath),
                    children: [],
                    permissions: await this.getFilePermissions(fullPath),
                };
                directories.push(dirNode);
                dirMap.set(relativePath, dirNode);
            }
            else if (stat.isFile() && stat.size <= maxFileSize) {
                const fileNode = {
                    path: relativePath,
                    name: path_1.default.basename(relativePath),
                    size: stat.size,
                    extension: path_1.default.extname(relativePath),
                    mimeType: mime_types_1.default.lookup(relativePath) || 'application/octet-stream',
                    lastModified: stat.mtime,
                    permissions: await this.getFilePermissions(fullPath),
                };
                // Add content for text files under a certain size
                if (this.isTextFile(fileNode) && stat.size < 10000) {
                    try {
                        fileNode.content = await fs_extra_1.default.readFile(fullPath, 'utf-8');
                    }
                    catch (error) {
                        logger_1.logger.debug(`Could not read file content: ${fullPath}`, error, 'ProjectDiscovery');
                    }
                }
                files.push(fileNode);
            }
        }
        // Build directory tree
        this.buildDirectoryTree(directories, files, dirMap);
        return {
            directories: directories.filter(dir => !dir.path.includes('/')), // Root level directories
            files: files.filter(file => !file.path.includes('/')), // Root level files
            totalFiles: files.length,
            totalDirectories: directories.length,
            lastIndexed: new Date(),
        };
    }
    buildDirectoryTree(directories, files, dirMap) {
        // Add files to their parent directories
        for (const file of files) {
            const parentPath = path_1.default.dirname(file.path);
            if (parentPath !== '.' && dirMap.has(parentPath)) {
                dirMap.get(parentPath).children.push(file);
            }
        }
        // Add subdirectories to their parent directories
        for (const dir of directories) {
            const parentPath = path_1.default.dirname(dir.path);
            if (parentPath !== '.' && dirMap.has(parentPath)) {
                dirMap.get(parentPath).children.push(dir);
            }
        }
    }
    async getFilePermissions(filePath) {
        try {
            const stat = await fs_extra_1.default.stat(filePath);
            const mode = stat.mode.toString(8);
            return {
                readable: !!(stat.mode & parseInt('400', 8)),
                writable: !!(stat.mode & parseInt('200', 8)),
                executable: !!(stat.mode & parseInt('100', 8)),
                mode,
            };
        }
        catch (error) {
            return {
                readable: false,
                writable: false,
                executable: false,
                mode: '000',
            };
        }
    }
    isTextFile(file) {
        const textExtensions = [
            '.txt', '.md', '.json', '.js', '.ts', '.jsx', '.tsx', '.py', '.rs', '.go',
            '.java', '.cs', '.cpp', '.c', '.h', '.hpp', '.css', '.scss', '.sass',
            '.html', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
            '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd'
        ];
        return textExtensions.includes(file.extension.toLowerCase()) ||
            file.mimeType.startsWith('text/') ||
            file.mimeType.includes('json') ||
            file.mimeType.includes('xml');
    }
    async extractDependencies(rootPath, projectType) {
        const dependencies = [];
        try {
            switch (projectType) {
                case 'nodejs':
                case 'web':
                    const packageJson = await this.safeReadJson(path_1.default.join(rootPath, 'package.json'));
                    if (packageJson) {
                        this.extractNpmDependencies(packageJson, dependencies);
                    }
                    break;
                case 'python':
                    await this.extractPythonDependencies(rootPath, dependencies);
                    break;
                case 'rust':
                    await this.extractRustDependencies(rootPath, dependencies);
                    break;
                case 'go':
                    await this.extractGoDependencies(rootPath, dependencies);
                    break;
                case 'java':
                    await this.extractJavaDependencies(rootPath, dependencies);
                    break;
            }
        }
        catch (error) {
            logger_1.logger.warn('Failed to extract dependencies', error, 'ProjectDiscovery');
        }
        return dependencies;
    }
    extractNpmDependencies(packageJson, dependencies) {
        const deps = packageJson.dependencies || {};
        const devDeps = packageJson.devDependencies || {};
        const peerDeps = packageJson.peerDependencies || {};
        const optionalDeps = packageJson.optionalDependencies || {};
        for (const [name, version] of Object.entries(deps)) {
            dependencies.push({
                name,
                version: version,
                type: 'production',
                source: 'package.json',
            });
        }
        for (const [name, version] of Object.entries(devDeps)) {
            dependencies.push({
                name,
                version: version,
                type: 'development',
                source: 'package.json',
            });
        }
        for (const [name, version] of Object.entries(peerDeps)) {
            dependencies.push({
                name,
                version: version,
                type: 'peer',
                source: 'package.json',
            });
        }
        for (const [name, version] of Object.entries(optionalDeps)) {
            dependencies.push({
                name,
                version: version,
                type: 'optional',
                source: 'package.json',
            });
        }
    }
    async extractPythonDependencies(rootPath, dependencies) {
        // Try requirements.txt
        const requirementsPath = path_1.default.join(rootPath, 'requirements.txt');
        if (fs_extra_1.default.existsSync(requirementsPath)) {
            const content = await fs_extra_1.default.readFile(requirementsPath, 'utf-8');
            const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
            for (const line of lines) {
                const match = line.match(/^([a-zA-Z0-9_-]+)([>=<~!]+)(.+)$/);
                if (match && match[1] && match[3]) {
                    dependencies.push({
                        name: match[1],
                        version: match[3],
                        type: 'production',
                        source: 'requirements.txt',
                    });
                }
            }
        }
        // Try pyproject.toml
        const pyprojectPath = path_1.default.join(rootPath, 'pyproject.toml');
        if (fs_extra_1.default.existsSync(pyprojectPath)) {
            // Basic TOML parsing for dependencies
            const content = await fs_extra_1.default.readFile(pyprojectPath, 'utf-8');
            const dependencySection = content.match(/\[tool\.poetry\.dependencies\]([\s\S]*?)(?=\[|$)/);
            if (dependencySection && dependencySection[1]) {
                const lines = dependencySection[1].split('\n').filter(line => line.trim());
                for (const line of lines) {
                    const match = line.match(/^([a-zA-Z0-9_-]+)\s*=\s*"([^"]+)"/);
                    if (match && match[1] && match[2]) {
                        dependencies.push({
                            name: match[1],
                            version: match[2],
                            type: 'production',
                            source: 'pyproject.toml',
                        });
                    }
                }
            }
        }
    }
    async extractRustDependencies(rootPath, dependencies) {
        const cargoPath = path_1.default.join(rootPath, 'Cargo.toml');
        if (fs_extra_1.default.existsSync(cargoPath)) {
            const content = await fs_extra_1.default.readFile(cargoPath, 'utf-8');
            // Basic TOML parsing for dependencies
            const dependencySection = content.match(/\[dependencies\]([\s\S]*?)(?=\[|$)/);
            if (dependencySection && dependencySection[1]) {
                const lines = dependencySection[1].split('\n').filter(line => line.trim());
                for (const line of lines) {
                    const match = line.match(/^([a-zA-Z0-9_-]+)\s*=\s*"([^"]+)"/);
                    if (match && match[1] && match[2]) {
                        dependencies.push({
                            name: match[1],
                            version: match[2],
                            type: 'production',
                            source: 'Cargo.toml',
                        });
                    }
                }
            }
        }
    }
    async extractGoDependencies(rootPath, dependencies) {
        const goModPath = path_1.default.join(rootPath, 'go.mod');
        if (fs_extra_1.default.existsSync(goModPath)) {
            const content = await fs_extra_1.default.readFile(goModPath, 'utf-8');
            const lines = content.split('\n');
            let inRequireBlock = false;
            for (const line of lines) {
                const trimmed = line.trim();
                if (trimmed === 'require (') {
                    inRequireBlock = true;
                    continue;
                }
                if (trimmed === ')' && inRequireBlock) {
                    inRequireBlock = false;
                    continue;
                }
                if (inRequireBlock || trimmed.startsWith('require ')) {
                    const match = trimmed.match(/([^\s]+)\s+([^\s]+)/);
                    if (match && match[1] && match[2]) {
                        dependencies.push({
                            name: match[1],
                            version: match[2],
                            type: 'production',
                            source: 'go.mod',
                        });
                    }
                }
            }
        }
    }
    async extractJavaDependencies(rootPath, dependencies) {
        // Try Maven pom.xml
        const pomPath = path_1.default.join(rootPath, 'pom.xml');
        if (fs_extra_1.default.existsSync(pomPath)) {
            const content = await fs_extra_1.default.readFile(pomPath, 'utf-8');
            // Basic XML parsing for Maven dependencies
            const dependencyMatches = content.matchAll(/<dependency>([\s\S]*?)<\/dependency>/g);
            for (const match of dependencyMatches) {
                const depContent = match[1];
                if (depContent) {
                    const groupId = depContent.match(/<groupId>(.*?)<\/groupId>/)?.[1];
                    const artifactId = depContent.match(/<artifactId>(.*?)<\/artifactId>/)?.[1];
                    const version = depContent.match(/<version>(.*?)<\/version>/)?.[1];
                    if (groupId && artifactId && version) {
                        dependencies.push({
                            name: `${groupId}:${artifactId}`,
                            version,
                            type: 'production',
                            source: 'pom.xml',
                        });
                    }
                }
            }
        }
    }
    async extractConfiguration(rootPath, projectType) {
        const config = {};
        try {
            switch (projectType) {
                case 'nodejs':
                case 'web':
                    await this.extractNodeConfiguration(rootPath, config);
                    break;
                case 'python':
                    await this.extractPythonConfiguration(rootPath, config);
                    break;
                case 'rust':
                    await this.extractRustConfiguration(rootPath, config);
                    break;
                case 'go':
                    await this.extractGoConfiguration(rootPath, config);
                    break;
            }
        }
        catch (error) {
            logger_1.logger.warn('Failed to extract configuration', error, 'ProjectDiscovery');
        }
        return config;
    }
    async extractNodeConfiguration(rootPath, config) {
        const packageJson = await this.safeReadJson(path_1.default.join(rootPath, 'package.json'));
        if (packageJson) {
            config.language = 'javascript';
            config.version = packageJson.version;
            // Detect package manager
            if (fs_extra_1.default.existsSync(path_1.default.join(rootPath, 'yarn.lock'))) {
                config.packageManager = 'yarn';
            }
            else if (fs_extra_1.default.existsSync(path_1.default.join(rootPath, 'pnpm-lock.yaml'))) {
                config.packageManager = 'pnpm';
            }
            else {
                config.packageManager = 'npm';
            }
            // Detect framework
            const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
            if (deps.react)
                config.framework = 'react';
            else if (deps.vue)
                config.framework = 'vue';
            else if (deps.angular)
                config.framework = 'angular';
            else if (deps.svelte)
                config.framework = 'svelte';
            else if (deps.next)
                config.framework = 'nextjs';
            else if (deps.nuxt)
                config.framework = 'nuxt';
            // Detect build tools
            if (deps.webpack)
                config.bundler = 'webpack';
            else if (deps.vite)
                config.bundler = 'vite';
            else if (deps.rollup)
                config.bundler = 'rollup';
            else if (deps.parcel)
                config.bundler = 'parcel';
            // Detect test framework
            if (deps.jest)
                config.testFramework = 'jest';
            else if (deps.mocha)
                config.testFramework = 'mocha';
            else if (deps.vitest)
                config.testFramework = 'vitest';
            // Detect linter
            if (deps.eslint)
                config.linter = 'eslint';
            if (deps.prettier)
                config.formatter = 'prettier';
            // Check for TypeScript
            if (deps.typescript || fs_extra_1.default.existsSync(path_1.default.join(rootPath, 'tsconfig.json'))) {
                config.language = 'typescript';
            }
        }
    }
    async extractPythonConfiguration(rootPath, config) {
        config.language = 'python';
        // Detect package manager
        if (fs_extra_1.default.existsSync(path_1.default.join(rootPath, 'pyproject.toml'))) {
            const content = await fs_extra_1.default.readFile(path_1.default.join(rootPath, 'pyproject.toml'), 'utf-8');
            if (content.includes('[tool.poetry]')) {
                config.packageManager = 'poetry';
            }
            else if (content.includes('[build-system]')) {
                config.packageManager = 'pip';
            }
        }
        else if (fs_extra_1.default.existsSync(path_1.default.join(rootPath, 'requirements.txt'))) {
            config.packageManager = 'pip';
        }
        // Detect framework
        const requirementsPath = path_1.default.join(rootPath, 'requirements.txt');
        if (fs_extra_1.default.existsSync(requirementsPath)) {
            const content = await fs_extra_1.default.readFile(requirementsPath, 'utf-8');
            if (content.includes('django'))
                config.framework = 'django';
            else if (content.includes('flask'))
                config.framework = 'flask';
            else if (content.includes('fastapi'))
                config.framework = 'fastapi';
        }
    }
    async extractRustConfiguration(rootPath, config) {
        config.language = 'rust';
        config.packageManager = 'cargo';
        config.buildTool = 'cargo';
        const cargoToml = await this.safeReadToml(path_1.default.join(rootPath, 'Cargo.toml'));
        if (cargoToml?.package?.version) {
            config.version = cargoToml.package.version;
        }
    }
    async extractGoConfiguration(rootPath, config) {
        config.language = 'go';
        config.packageManager = 'go mod';
        config.buildTool = 'go build';
        const goModPath = path_1.default.join(rootPath, 'go.mod');
        if (fs_extra_1.default.existsSync(goModPath)) {
            const content = await fs_extra_1.default.readFile(goModPath, 'utf-8');
            const versionMatch = content.match(/go (\d+\.\d+)/);
            if (versionMatch && versionMatch[1]) {
                config.version = versionMatch[1];
            }
        }
    }
    async extractGitInfo(rootPath) {
        try {
            const gitDir = path_1.default.join(rootPath, '.git');
            if (!fs_extra_1.default.existsSync(gitDir)) {
                return undefined;
            }
            const headPath = path_1.default.join(gitDir, 'HEAD');
            if (!fs_extra_1.default.existsSync(headPath)) {
                return undefined;
            }
            const headContent = await fs_extra_1.default.readFile(headPath, 'utf-8');
            const branch = headContent.startsWith('ref: refs/heads/')
                ? headContent.replace('ref: refs/heads/', '').trim()
                : 'detached';
            // Get current commit
            let commit = '';
            if (branch !== 'detached') {
                const branchRefPath = path_1.default.join(gitDir, 'refs', 'heads', branch);
                if (fs_extra_1.default.existsSync(branchRefPath)) {
                    commit = (await fs_extra_1.default.readFile(branchRefPath, 'utf-8')).trim();
                }
            }
            else {
                commit = headContent.trim();
            }
            return {
                branch,
                commit: commit.slice(0, 7), // Short commit hash
                isRepo: true,
                status: {
                    staged: [],
                    unstaged: [],
                    untracked: [],
                    ahead: 0,
                    behind: 0,
                },
            };
        }
        catch (error) {
            logger_1.logger.debug('Failed to extract git info', error, 'ProjectDiscovery');
            return undefined;
        }
    }
    async safeReadJson(filePath) {
        try {
            if (fs_extra_1.default.existsSync(filePath)) {
                const content = await fs_extra_1.default.readFile(filePath, 'utf-8');
                return JSON.parse(content);
            }
        }
        catch (error) {
            logger_1.logger.debug(`Failed to read JSON file: ${filePath}`, error, 'ProjectDiscovery');
        }
        return null;
    }
    async safeReadToml(filePath) {
        try {
            if (fs_extra_1.default.existsSync(filePath)) {
                const content = await fs_extra_1.default.readFile(filePath, 'utf-8');
                // Basic TOML parsing - you might want to use a proper TOML library
                const result = {};
                const lines = content.split('\n');
                let currentSection = result;
                for (const line of lines) {
                    const trimmed = line.trim();
                    if (!trimmed || trimmed.startsWith('#'))
                        continue;
                    if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                        const sectionName = trimmed.slice(1, -1);
                        const parts = sectionName.split('.');
                        currentSection = result;
                        for (const part of parts) {
                            if (!currentSection[part]) {
                                currentSection[part] = {};
                            }
                            currentSection = currentSection[part];
                        }
                    }
                    else {
                        const match = trimmed.match(/^([^=]+)=(.+)$/);
                        if (match && match[1] && match[2]) {
                            const key = match[1].trim();
                            let value = match[2].trim();
                            // Remove quotes
                            if ((value.startsWith('"') && value.endsWith('"')) ||
                                (value.startsWith("'") && value.endsWith("'"))) {
                                value = value.slice(1, -1);
                            }
                            currentSection[key] = value;
                        }
                    }
                }
                return result;
            }
        }
        catch (error) {
            logger_1.logger.debug(`Failed to read TOML file: ${filePath}`, error, 'ProjectDiscovery');
        }
        return null;
    }
    isCacheValid(context) {
        const maxAge = 5 * 60 * 1000; // 5 minutes
        const age = Date.now() - context.structure.lastIndexed.getTime();
        return age < maxAge;
    }
    setupFileWatcher(rootPath) {
        if (this.watchers.has(rootPath)) {
            return; // Already watching
        }
        const excludePatterns = config_1.config.getConfig().context.excludePatterns;
        const watcher = chokidar_1.default.watch(rootPath, {
            ignored: excludePatterns,
            persistent: true,
            ignoreInitial: true,
        });
        watcher.on('all', (event, filePath) => {
            logger_1.logger.debug(`File ${event}: ${filePath}`, undefined, 'ProjectDiscovery');
            // Invalidate cache when files change
            this.indexCache.delete(rootPath);
            // Emit event for real-time updates
            this.emit('fileChange', { event, filePath, rootPath });
        });
        this.watchers.set(rootPath, watcher);
        logger_1.logger.info(`Started watching files in: ${rootPath}`, undefined, 'ProjectDiscovery');
    }
    createFileIndex(structure) {
        const fileIndex = {
            files: new Map(),
            directories: new Map(),
            byExtension: new Map(),
            bySize: new Map(),
            searchIndex: new Map(),
        };
        const processNode = (node, parentPath = '') => {
            const fullPath = parentPath ? `${parentPath}/${node.path}` : node.path;
            if ('size' in node) {
                // It's a file
                const file = node;
                fileIndex.files.set(fullPath, file);
                // Index by extension
                const ext = file.extension.toLowerCase();
                if (!fileIndex.byExtension.has(ext)) {
                    fileIndex.byExtension.set(ext, []);
                }
                fileIndex.byExtension.get(ext).push(file);
                // Index by size category
                const sizeCategory = this.getSizeCategory(file.size);
                if (!fileIndex.bySize.has(sizeCategory)) {
                    fileIndex.bySize.set(sizeCategory, []);
                }
                fileIndex.bySize.get(sizeCategory).push(file);
                // Create search index
                const searchTerms = [
                    file.name.toLowerCase(),
                    file.extension.toLowerCase(),
                    ...file.name.toLowerCase().split(/[._-]/)
                ];
                for (const term of searchTerms) {
                    if (!fileIndex.searchIndex.has(term)) {
                        fileIndex.searchIndex.set(term, []);
                    }
                    fileIndex.searchIndex.get(term).push(file);
                }
            }
            else {
                // It's a directory
                const dir = node;
                fileIndex.directories.set(fullPath, dir);
                // Process children
                for (const child of dir.children) {
                    processNode(child, fullPath);
                }
            }
        };
        // Process root level items
        for (const file of structure.files) {
            processNode(file);
        }
        for (const dir of structure.directories) {
            processNode(dir);
        }
        return fileIndex;
    }
    getSizeCategory(size) {
        if (size < 1024)
            return 'tiny'; // < 1KB
        if (size < 10 * 1024)
            return 'small'; // < 10KB
        if (size < 100 * 1024)
            return 'medium'; // < 100KB
        if (size < 1024 * 1024)
            return 'large'; // < 1MB
        return 'huge'; // >= 1MB
    }
    async refreshProject(rootPath) {
        this.indexCache.delete(rootPath);
        return this.discoverProject(rootPath);
    }
    stopWatching(rootPath) {
        const watcher = this.watchers.get(rootPath);
        if (watcher) {
            watcher.close();
            this.watchers.delete(rootPath);
            logger_1.logger.info(`Stopped watching files in: ${rootPath}`, undefined, 'ProjectDiscovery');
        }
    }
    stopAllWatching() {
        for (const [rootPath, watcher] of this.watchers) {
            watcher.close();
            logger_1.logger.info(`Stopped watching files in: ${rootPath}`, undefined, 'ProjectDiscovery');
        }
        this.watchers.clear();
    }
}
exports.ProjectDiscovery = ProjectDiscovery;
exports.projectDiscovery = new ProjectDiscovery();
//# sourceMappingURL=discovery.js.map
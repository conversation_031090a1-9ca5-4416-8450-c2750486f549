import { ConfigManager } from './index';

describe('ConfigManager', () => {
  let configManager: ConfigManager;

  beforeEach(() => {
    configManager = ConfigManager.getInstance();
    // Reset configuration to defaults for each test
    configManager.resetConfig();
  });

  it('should return default configuration', () => {
    const config = configManager.getConfig();
    
    expect(config.defaultProvider).toBe('deepseek');
    expect(config.providers.deepseek.defaultModel).toBe('deepseek-chat');
    expect(config.providers.ollama.defaultModel).toBe('llama3.2');
    expect(config.session.autoSave).toBe(true);
    expect(config.context.autoIndex).toBe(true);
    expect(config.tools.allowShellExecution).toBe(true);
    expect(config.tools.allowFileOperations).toBe(true);
  });

  it('should validate provider configuration', () => {
    // Clear environment variable for test
    const originalEnv = process.env['DEEPSEEK_API_KEY'];
    delete process.env['DEEPSEEK_API_KEY'];

    // Deepseek without API key should be invalid
    expect(configManager.validateProviderConfig('deepseek')).toBe(false);

    // Ollama should be valid by default
    expect(configManager.validateProviderConfig('ollama')).toBe(true);

    // Restore environment variable
    if (originalEnv) {
      process.env['DEEPSEEK_API_KEY'] = originalEnv;
    }
  });

  it('should get provider configuration', () => {
    const deepseekConfig = configManager.getProviderConfig('deepseek');
    expect(deepseekConfig.baseUrl).toBe('https://api.deepseek.com');
    expect(deepseekConfig.defaultModel).toBe('deepseek-chat');

    const ollamaConfig = configManager.getProviderConfig('ollama');
    expect(ollamaConfig.baseUrl).toBe('http://localhost:11434');
    expect(ollamaConfig.defaultModel).toBe('llama3.2');
  });

  it('should get environment variables', () => {
    const envVars = configManager.getEnvironmentVariables();
    
    expect(envVars['AGENTIC_CLI_CONFIG_PATH']).toBeDefined();
    expect(envVars['AGENTIC_CLI_DATA_DIR']).toBeDefined();
    expect(envVars['AGENTIC_CLI_SESSIONS_DIR']).toBeDefined();
    expect(envVars['AGENTIC_CLI_CACHE_DIR']).toBeDefined();
    expect(envVars['AGENTIC_CLI_LOGS_DIR']).toBeDefined();
  });

  it('should update configuration', () => {
    const originalConfig = configManager.getConfig();
    
    configManager.updateConfig({
      defaultProvider: 'ollama',
      session: {
        ...originalConfig.session,
        maxHistory: 500,
      },
    });

    const updatedConfig = configManager.getConfig();
    expect(updatedConfig.defaultProvider).toBe('ollama');
    expect(updatedConfig.session.maxHistory).toBe(500);
  });
});

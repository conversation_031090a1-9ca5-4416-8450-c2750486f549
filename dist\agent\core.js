"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentCore = exports.Agent = void 0;
const nanoid_1 = require("nanoid");
const providers_1 = require("../providers");
const tools_1 = require("../tools");
const manager_1 = require("../session/manager");
const discovery_1 = require("../context/discovery");
const logger_1 = require("../utils/logger");
class Agent {
    id;
    config;
    context;
    isProcessing = false;
    constructor(agentConfig, workingDirectory) {
        this.id = (0, nanoid_1.nanoid)();
        this.config = agentConfig;
        // Initialize context - will be properly set when session is created/loaded
        this.context = {
            sessionId: '',
            workingDirectory: workingDirectory || process.cwd(),
            environment: process.env,
            projectContext: {}, // Will be set by session manager
            agent: this,
        };
        logger_1.logger.info(`Agent created`, {
            agentId: this.id,
            provider: agentConfig.provider,
            model: agentConfig.model
        }, 'Agent');
    }
    async initialize(sessionId) {
        try {
            let session;
            if (sessionId) {
                // Load existing session
                session = await manager_1.sessionManager.loadSession(sessionId);
                logger_1.logger.info(`Loaded existing session`, { sessionId }, 'Agent', this.id);
            }
            else {
                // Create new session
                session = await manager_1.sessionManager.createSession(undefined, this.context.workingDirectory);
                logger_1.logger.info(`Created new session`, { sessionId: session.id }, 'Agent', this.id);
            }
            // Update context with session information
            this.context = {
                sessionId: session.id,
                workingDirectory: session.workingDirectory,
                environment: session.context.environment,
                projectContext: {
                    root: session.workingDirectory,
                    type: session.metadata['projectType'],
                    structure: session.context.projectStructure,
                    dependencies: session.context.dependencies,
                    configuration: {},
                    gitInfo: session.context.gitInfo,
                },
                agent: this,
            };
            logger_1.logger.info(`Agent initialized successfully`, {
                agentId: this.id,
                sessionId: session.id,
                projectType: session.metadata['projectType']
            }, 'Agent', this.id);
        }
        catch (error) {
            logger_1.logger.error(`Failed to initialize agent`, error, 'Agent', this.id);
            throw error;
        }
    }
    async sendMessage(message) {
        if (this.isProcessing) {
            throw new Error('Agent is currently processing another request');
        }
        this.isProcessing = true;
        try {
            logger_1.logger.info(`Processing message`, {
                messageLength: message.length
            }, 'Agent', this.context.sessionId);
            // Add user message to session
            const userMessage = {
                role: 'user',
                content: message,
            };
            await manager_1.sessionManager.addMessage(userMessage);
            // Get conversation history
            const messages = manager_1.sessionManager.getMessages();
            // Get LLM provider
            const provider = providers_1.providerManager.getProvider(this.config.provider);
            if (!provider) {
                throw new Error(`Provider not found: ${this.config.provider}`);
            }
            // Validate provider configuration
            if (!provider.validateConfig(this.config)) {
                throw new Error(`Invalid configuration for provider: ${this.config.provider}`);
            }
            // Get available tools
            const tools = tools_1.toolRegistry.getAllTools();
            // Send message to LLM with tools
            const response = await provider.sendToolMessage(messages, tools, this.config);
            // Process tool calls if any
            if (response.toolCalls && response.toolCalls.length > 0) {
                const toolResults = await this.executeTools(response.toolCalls);
                // Add assistant message with tool calls
                const assistantMessage = {
                    role: 'assistant',
                    content: response.message,
                    toolCalls: response.toolCalls,
                };
                await manager_1.sessionManager.addMessage(assistantMessage);
                // Add tool results as messages
                for (const result of toolResults) {
                    const toolMessage = {
                        role: 'tool',
                        content: JSON.stringify(result.result),
                        toolCallId: result.toolCallId,
                    };
                    await manager_1.sessionManager.addMessage(toolMessage);
                }
                // Get final response from LLM after tool execution
                const finalMessages = manager_1.sessionManager.getMessages();
                const finalResponse = await provider.sendMessage(finalMessages, this.config);
                // Add final assistant message
                const finalAssistantMessage = {
                    role: 'assistant',
                    content: finalResponse,
                };
                await manager_1.sessionManager.addMessage(finalAssistantMessage);
                logger_1.logger.info(`Message processed with tools`, {
                    toolCallsCount: response.toolCalls.length,
                    responseLength: finalResponse.length
                }, 'Agent', this.context.sessionId);
                return finalResponse;
            }
            else {
                // No tool calls, just add the response
                const assistantMessage = {
                    role: 'assistant',
                    content: response.message,
                };
                await manager_1.sessionManager.addMessage(assistantMessage);
                logger_1.logger.info(`Message processed without tools`, {
                    responseLength: response.message.length
                }, 'Agent', this.context.sessionId);
                return response.message;
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to process message`, error, 'Agent', this.context.sessionId);
            throw new Error(`Failed to process message: ${error.message}`);
        }
        finally {
            this.isProcessing = false;
        }
    }
    async executeTools(toolCalls) {
        const results = [];
        logger_1.logger.info(`Executing ${toolCalls.length} tool calls`, undefined, 'Agent', this.context.sessionId);
        // Check if we should execute tools in parallel
        const shouldExecuteInParallel = toolCalls.length > 1 && this.canExecuteInParallel(toolCalls);
        if (shouldExecuteInParallel) {
            return this.executeToolsInParallel(toolCalls);
        }
        // Sequential execution for dependent or unsafe tools
        for (const toolCall of toolCalls) {
            const result = await this.executeSingleTool(toolCall);
            results.push(result);
        }
        logger_1.logger.info(`Tool execution completed`, {
            totalTools: toolCalls.length,
            successful: results.filter(r => !r.error).length,
            failed: results.filter(r => r.error).length
        }, 'Agent', this.context.sessionId);
        return results;
    }
    async executeSingleTool(toolCall) {
        try {
            const tool = tools_1.toolRegistry.getTool(toolCall.function.name);
            if (!tool) {
                return {
                    toolCallId: toolCall.id,
                    result: { error: `Tool not found: ${toolCall.function.name}` },
                    error: 'TOOL_NOT_FOUND',
                };
            }
            // Parse tool arguments
            let args;
            try {
                args = JSON.parse(toolCall.function.arguments);
            }
            catch (error) {
                return {
                    toolCallId: toolCall.id,
                    result: { error: `Invalid tool arguments: ${toolCall.function.arguments}` },
                    error: 'INVALID_ARGUMENTS',
                };
            }
            // Validate arguments against tool schema
            const validationResult = tool.parameters.safeParse(args);
            if (!validationResult.success) {
                return {
                    toolCallId: toolCall.id,
                    result: {
                        error: `Invalid arguments for tool ${tool.name}`,
                        details: validationResult.error.issues
                    },
                    error: 'VALIDATION_ERROR',
                };
            }
            logger_1.logger.debug(`Executing tool: ${tool.name}`, { args }, 'Agent', this.context.sessionId);
            // Execute the tool
            const startTime = Date.now();
            const result = await tool.execute(validationResult.data, this.context);
            const duration = Date.now() - startTime;
            logger_1.logger.debug(`Tool executed: ${tool.name}`, {
                duration,
                success: result.success
            }, 'Agent', this.context.sessionId);
            return {
                toolCallId: toolCall.id,
                result,
            };
        }
        catch (error) {
            logger_1.logger.error(`Tool execution failed: ${toolCall.function.name}`, error, 'Agent', this.context.sessionId);
            return {
                toolCallId: toolCall.id,
                result: {
                    error: `Tool execution failed: ${error.message}`,
                    toolName: toolCall.function.name
                },
                error: 'EXECUTION_ERROR',
            };
        }
    }
    canExecuteInParallel(toolCalls) {
        // Tools that should not be executed in parallel
        const sequentialTools = [
            'write_file', 'delete_file', 'move_file', 'copy_file',
            'execute_command', 'execute_script', 'create_directory'
        ];
        // Check if any tool calls contain sequential tools
        return !toolCalls.some(call => sequentialTools.includes(call.function.name));
    }
    async executeToolsInParallel(toolCalls) {
        logger_1.logger.info(`Executing ${toolCalls.length} tools in parallel`, undefined, 'Agent', this.context.sessionId);
        const promises = toolCalls.map(toolCall => this.executeSingleTool(toolCall));
        const results = await Promise.all(promises);
        logger_1.logger.info(`Parallel tool execution completed`, {
            totalTools: toolCalls.length,
            successful: results.filter(r => !r.error).length,
            failed: results.filter(r => r.error).length
        }, 'Agent', this.context.sessionId);
        return results;
    }
    getContext() {
        return { ...this.context };
    }
    async updateWorkingDirectory(newPath) {
        try {
            logger_1.logger.info(`Updating working directory`, {
                from: this.context.workingDirectory,
                to: newPath
            }, 'Agent', this.context.sessionId);
            // Discover new project context
            const projectContext = await discovery_1.projectDiscovery.discoverProject(newPath);
            // Update context
            this.context.workingDirectory = newPath;
            this.context.projectContext = projectContext;
            // Update session if available
            const currentSession = manager_1.sessionManager.getCurrentSession();
            if (currentSession) {
                currentSession.workingDirectory = newPath;
                await manager_1.sessionManager.refreshSessionContext(currentSession);
            }
            logger_1.logger.info(`Working directory updated successfully`, {
                newPath,
                projectType: projectContext.type
            }, 'Agent', this.context.sessionId);
        }
        catch (error) {
            logger_1.logger.error(`Failed to update working directory`, error, 'Agent', this.context.sessionId);
            throw error;
        }
    }
    async refreshContext() {
        try {
            logger_1.logger.info(`Refreshing agent context`, undefined, 'Agent', this.context.sessionId);
            const projectContext = await discovery_1.projectDiscovery.refreshProject(this.context.workingDirectory);
            this.context.projectContext = projectContext;
            const currentSession = manager_1.sessionManager.getCurrentSession();
            if (currentSession) {
                await manager_1.sessionManager.refreshSessionContext(currentSession);
            }
            logger_1.logger.info(`Agent context refreshed`, {
                files: projectContext.structure.totalFiles
            }, 'Agent', this.context.sessionId);
        }
        catch (error) {
            logger_1.logger.error(`Failed to refresh context`, error, 'Agent', this.context.sessionId);
            throw error;
        }
    }
    getSessionId() {
        return this.context.sessionId;
    }
    isReady() {
        return !!this.context.sessionId && !this.isProcessing;
    }
    async cleanup() {
        try {
            logger_1.logger.info(`Cleaning up agent`, undefined, 'Agent', this.context.sessionId);
            // Save current session
            const currentSession = manager_1.sessionManager.getCurrentSession();
            if (currentSession) {
                await manager_1.sessionManager.saveSession(currentSession);
            }
            // Stop file watching
            discovery_1.projectDiscovery.stopWatching(this.context.workingDirectory);
            logger_1.logger.info(`Agent cleanup completed`, undefined, 'Agent', this.context.sessionId);
        }
        catch (error) {
            logger_1.logger.error(`Failed to cleanup agent`, error, 'Agent', this.context.sessionId);
        }
    }
}
exports.Agent = Agent;
exports.AgentCore = Agent;
//# sourceMappingURL=core.js.map